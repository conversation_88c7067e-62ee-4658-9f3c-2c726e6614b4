/*
 * PrivacyManager.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.prefs.Preferences;

/**
 * Manages privacy settings for AI analysis.
 * Controls what data is included when sending floor plans to AI services.
 * 
 * <AUTHOR>
 */
public class PrivacyManager {
  private static final String EXCLUDE_PERSONAL_INFO = "excludePersonalInfo";
  private static final String EXCLUDE_LOCATION_DATA = "excludeLocationData";
  private static final String EXCLUDE_FURNITURE_DETAILS = "excludeFurnitureDetails";
  
  private final Preferences preferences;
  
  /**
   * Creates a new privacy manager.
   */
  public PrivacyManager() {
    this.preferences = Preferences.userNodeForPackage(PrivacyManager.class);
  }
  
  /**
   * Returns whether personal information should be excluded from AI analysis.
   */
  public boolean shouldExcludePersonalInfo() {
    return preferences.getBoolean(EXCLUDE_PERSONAL_INFO, false);
  }
  
  /**
   * Sets whether personal information should be excluded from AI analysis.
   */
  public void setExcludePersonalInfo(boolean exclude) {
    preferences.putBoolean(EXCLUDE_PERSONAL_INFO, exclude);
  }
  
  /**
   * Returns whether location data should be excluded from AI analysis.
   */
  public boolean shouldExcludeLocationData() {
    return preferences.getBoolean(EXCLUDE_LOCATION_DATA, false);
  }
  
  /**
   * Sets whether location data should be excluded from AI analysis.
   */
  public void setExcludeLocationData(boolean exclude) {
    preferences.putBoolean(EXCLUDE_LOCATION_DATA, exclude);
  }
  
  /**
   * Returns whether furniture details should be excluded from AI analysis.
   */
  public boolean shouldExcludeFurnitureDetails() {
    return preferences.getBoolean(EXCLUDE_FURNITURE_DETAILS, false);
  }
  
  /**
   * Sets whether furniture details should be excluded from AI analysis.
   */
  public void setExcludeFurnitureDetails(boolean exclude) {
    preferences.putBoolean(EXCLUDE_FURNITURE_DETAILS, exclude);
  }
  
  /**
   * Sanitizes floor plan data based on privacy settings.
   * 
   * @param floorPlanData The original floor plan data in JSON format
   * @param includePersonalInfo Whether to include personal information
   * @return Sanitized floor plan data
   */
  public String sanitizeFloorPlanData(String floorPlanData, boolean includePersonalInfo) {
    if (includePersonalInfo && !shouldExcludePersonalInfo()) {
      return floorPlanData; // Return original data
    }
    
    String sanitized = floorPlanData;
    
    // Remove location data if privacy setting is enabled
    if (shouldExcludeLocationData()) {
      sanitized = removeLocationData(sanitized);
    }
    
    // Remove furniture details if privacy setting is enabled
    if (shouldExcludeFurnitureDetails()) {
      sanitized = removeFurnitureDetails(sanitized);
    }
    
    // Remove personal information if privacy setting is enabled
    if (shouldExcludePersonalInfo()) {
      sanitized = removePersonalInfo(sanitized);
    }
    
    return sanitized;
  }
  
  /**
   * Removes location data from the floor plan JSON.
   */
  private String removeLocationData(String json) {
    // Remove compass latitude/longitude and timezone
    json = json.replaceAll("\"latitude\":\\s*[^,}]+,?", "");
    json = json.replaceAll("\"longitude\":\\s*[^,}]+,?", "");
    json = json.replaceAll("\"timeZone\":\\s*\"[^\"]*\",?", "");
    
    // Clean up any trailing commas
    json = json.replaceAll(",\\s*}", "}");
    json = json.replaceAll(",\\s*]", "]");
    
    return json;
  }
  
  /**
   * Removes detailed furniture information from the floor plan JSON.
   */
  private String removeFurnitureDetails(String json) {
    // Replace furniture array with simplified version
    json = json.replaceAll("\"furniture\":\\s*\\[[^\\]]*\\]", "\"furniture\": []");
    
    return json;
  }
  
  /**
   * Removes personal information from the floor plan JSON.
   */
  private String removePersonalInfo(String json) {
    // Remove home name
    json = json.replaceAll("\"name\":\\s*\"[^\"]*\"", "\"name\": \"[REDACTED]\"");
    
    // Remove specific furniture names and catalog IDs
    json = json.replaceAll("\"catalogId\":\\s*\"[^\"]*\"", "\"catalogId\": \"[REDACTED]\"");
    
    return json;
  }
  
  /**
   * Returns a summary of current privacy settings.
   */
  public String getPrivacySettingsSummary() {
    StringBuilder summary = new StringBuilder();
    summary.append("Privacy Settings:\n");
    summary.append("- Exclude Personal Info: ").append(shouldExcludePersonalInfo()).append("\n");
    summary.append("- Exclude Location Data: ").append(shouldExcludeLocationData()).append("\n");
    summary.append("- Exclude Furniture Details: ").append(shouldExcludeFurnitureDetails()).append("\n");
    return summary.toString();
  }
}
