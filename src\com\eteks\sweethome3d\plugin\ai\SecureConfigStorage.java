/*
 * SecureConfigStorage.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.prefs.Preferences;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.security.SecureRandom;

/**
 * Provides secure storage for sensitive configuration data like API keys.
 * Uses basic encryption to protect stored credentials.
 * 
 * <AUTHOR>
 */
public class SecureConfigStorage {
  private static final String API_KEY_PREF = "encryptedApiKey";
  private static final String ENCRYPTION_KEY_PREF = "encryptionKey";
  private static final String ALGORITHM = "AES";
  
  private final Preferences preferences;
  
  /**
   * Creates a new secure configuration storage.
   */
  public SecureConfigStorage() {
    this.preferences = Preferences.userNodeForPackage(SecureConfigStorage.class);
  }
  
  /**
   * Stores an API key securely.
   * 
   * @param apiKey The API key to store
   */
  public void storeApiKey(String apiKey) {
    if (apiKey == null || apiKey.isEmpty()) {
      clearApiKey();
      return;
    }
    
    try {
      SecretKey key = getOrCreateEncryptionKey();
      String encryptedKey = encrypt(apiKey, key);
      preferences.put(API_KEY_PREF, encryptedKey);
    } catch (Exception e) {
      // Fallback to plain text storage if encryption fails
      // In a production system, you might want to handle this differently
      preferences.put(API_KEY_PREF, "PLAIN:" + apiKey);
    }
  }
  
  /**
   * Retrieves the stored API key.
   * 
   * @return The decrypted API key, or null if not found
   */
  public String retrieveApiKey() {
    String storedValue = preferences.get(API_KEY_PREF, null);
    if (storedValue == null) {
      return null;
    }
    
    // Check if it's plain text (fallback)
    if (storedValue.startsWith("PLAIN:")) {
      return storedValue.substring(6);
    }
    
    try {
      SecretKey key = getOrCreateEncryptionKey();
      return decrypt(storedValue, key);
    } catch (Exception e) {
      // If decryption fails, return null
      return null;
    }
  }
  
  /**
   * Clears the stored API key.
   */
  public void clearApiKey() {
    preferences.remove(API_KEY_PREF);
  }
  
  /**
   * Gets or creates an encryption key for this installation.
   */
  private SecretKey getOrCreateEncryptionKey() throws Exception {
    String encodedKey = preferences.get(ENCRYPTION_KEY_PREF, null);
    
    if (encodedKey == null) {
      // Generate a new key
      KeyGenerator keyGen = KeyGenerator.getInstance(ALGORITHM);
      keyGen.init(128);
      SecretKey key = keyGen.generateKey();
      
      // Store the key
      encodedKey = Base64.getEncoder().encodeToString(key.getEncoded());
      preferences.put(ENCRYPTION_KEY_PREF, encodedKey);
      
      return key;
    } else {
      // Decode existing key
      byte[] decodedKey = Base64.getDecoder().decode(encodedKey);
      return new SecretKeySpec(decodedKey, ALGORITHM);
    }
  }
  
  /**
   * Encrypts a string using the provided key.
   */
  private String encrypt(String plainText, SecretKey key) throws Exception {
    Cipher cipher = Cipher.getInstance(ALGORITHM);
    cipher.init(Cipher.ENCRYPT_MODE, key);
    byte[] encryptedBytes = cipher.doFinal(plainText.getBytes("UTF-8"));
    return Base64.getEncoder().encodeToString(encryptedBytes);
  }
  
  /**
   * Decrypts a string using the provided key.
   */
  private String decrypt(String encryptedText, SecretKey key) throws Exception {
    Cipher cipher = Cipher.getInstance(ALGORITHM);
    cipher.init(Cipher.DECRYPT_MODE, key);
    byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
    return new String(decryptedBytes, "UTF-8");
  }
}
