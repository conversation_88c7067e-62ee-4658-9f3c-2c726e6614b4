@echo off
REM Test script for AI Floor Plan Analysis Plugin
REM This script validates the plugin structure and attempts to build it

echo ========================================
echo AI Floor Plan Analysis Plugin Test
echo ========================================
echo.

echo Checking Java version...
java -version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java not found or not in PATH
    pause
    exit /b 1
)
echo.

echo Checking project structure...
if not exist "src\com\eteks\sweethome3d\plugin\ai\AIPlugin.java" (
    echo ERROR: Main plugin file not found
    pause
    exit /b 1
)

if not exist "src\ApplicationPlugin.properties" (
    echo ERROR: Plugin properties file not found
    pause
    exit /b 1
)

if not exist "SweetHome3D-7.5.jar" (
    echo ERROR: SweetHome3D-7.5.jar not found in project root
    echo Please ensure SweetHome3D-7.5.jar is in the project directory
    pause
    exit /b 1
)

echo Project structure OK
echo.

echo Checking source files...
set /a file_count=0
for /r src %%f in (*.java) do (
    set /a file_count+=1
    echo Found: %%f
)
echo Total Java files: %file_count%
echo.

echo Attempting to compile plugin...
if not exist "build" mkdir build

javac -cp "SweetHome3D-7.5.jar" -d build src\com\eteks\sweethome3d\plugin\ai\*.java
if %ERRORLEVEL% neq 0 (
    echo ERROR: Compilation failed
    pause
    exit /b 1
)

echo Compilation successful!
echo.

echo Copying resources...
copy "src\ApplicationPlugin*.properties" "build\" >nul
if %ERRORLEVEL% neq 0 (
    echo WARNING: Could not copy properties files
)

echo.
echo Creating plugin JAR...
cd build
jar cf ..\AiFloorPlanAnalysisPlugin.jar com\eteks\sweethome3d\plugin\ai\*.class ApplicationPlugin*.properties
cd ..

if exist "AiFloorPlanAnalysisPlugin.jar" (
    echo SUCCESS: Plugin JAR created successfully!
    echo File: AiFloorPlanAnalysisPlugin.jar
    echo.
    echo To install the plugin:
    echo 1. Copy AiFloorPlanAnalysisPlugin.jar to your Sweet Home 3D plugins directory
    echo 2. Restart Sweet Home 3D
    echo 3. Look for "AI Analysis" in the Tools menu
) else (
    echo ERROR: Failed to create plugin JAR
    pause
    exit /b 1
)

echo.
echo ========================================
echo Plugin build completed successfully!
echo ========================================
pause
