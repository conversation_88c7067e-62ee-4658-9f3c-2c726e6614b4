/*
 * AIErrorHandler.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>na
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.concurrent.CompletionException;

/**
 * Handles and categorizes errors from AI operations.
 * Provides user-friendly error messages and suggestions for common issues.
 * 
 * <AUTHOR>
 */
public class AIErrorHandler {
  
  /**
   * Converts an exception to a user-friendly error message.
   * 
   * @param throwable The exception to handle
   * @return A user-friendly error message
   */
  public static String getErrorMessage(Throwable throwable) {
    if (throwable == null) {
      return "Unknown error occurred";
    }
    
    // Unwrap CompletionException
    if (throwable instanceof CompletionException && throwable.getCause() != null) {
      throwable = throwable.getCause();
    }
    
    // Handle specific exception types
    if (throwable instanceof UnknownHostException) {
      return "Cannot connect to AI service. Please check your internet connection and the base URL.";
    }
    
    if (throwable instanceof ConnectException) {
      return "Connection refused. The AI service may be unavailable or the URL may be incorrect.";
    }
    
    if (throwable instanceof SocketTimeoutException) {
      return "Request timed out. The AI service may be overloaded or your internet connection may be slow.";
    }
    
    if (throwable instanceof IOException) {
      String message = throwable.getMessage();
      if (message != null) {
        if (message.contains("401")) {
          return "Authentication failed. Please check your API key.";
        }
        if (message.contains("403")) {
          return "Access forbidden. Your API key may not have permission to use this model.";
        }
        if (message.contains("404")) {
          return "Model not found. Please check the model name.";
        }
        if (message.contains("429")) {
          return "Rate limit exceeded. Please wait a moment and try again.";
        }
        if (message.contains("500")) {
          return "AI service error. Please try again later.";
        }
        if (message.contains("502") || message.contains("503") || message.contains("504")) {
          return "AI service temporarily unavailable. Please try again later.";
        }
      }
      return "Network error: " + (message != null ? message : "Connection failed");
    }
    
    if (throwable instanceof IllegalArgumentException) {
      return "Configuration error: " + throwable.getMessage();
    }
    
    if (throwable instanceof IllegalStateException) {
      return "Service error: " + throwable.getMessage();
    }
    
    // Generic error handling
    String message = throwable.getMessage();
    if (message != null && !message.trim().isEmpty()) {
      return "Error: " + message;
    }
    
    return "An unexpected error occurred: " + throwable.getClass().getSimpleName();
  }
  
  /**
   * Determines if an error is likely due to a configuration issue.
   * 
   * @param throwable The exception to analyze
   * @return true if the error is likely a configuration issue
   */
  public static boolean isConfigurationError(Throwable throwable) {
    if (throwable == null) {
      return false;
    }
    
    // Unwrap CompletionException
    if (throwable instanceof CompletionException && throwable.getCause() != null) {
      throwable = throwable.getCause();
    }
    
    if (throwable instanceof IllegalArgumentException) {
      return true;
    }
    
    if (throwable instanceof IOException) {
      String message = throwable.getMessage();
      if (message != null) {
        return message.contains("401") || message.contains("403") || message.contains("404");
      }
    }
    
    return false;
  }
  
  /**
   * Determines if an error is likely temporary and retrying might succeed.
   * 
   * @param throwable The exception to analyze
   * @return true if the error is likely temporary
   */
  public static boolean isTemporaryError(Throwable throwable) {
    if (throwable == null) {
      return false;
    }
    
    // Unwrap CompletionException
    if (throwable instanceof CompletionException && throwable.getCause() != null) {
      throwable = throwable.getCause();
    }
    
    if (throwable instanceof SocketTimeoutException) {
      return true;
    }
    
    if (throwable instanceof IOException) {
      String message = throwable.getMessage();
      if (message != null) {
        return message.contains("429") || message.contains("500") || 
               message.contains("502") || message.contains("503") || message.contains("504");
      }
    }
    
    return false;
  }
  
  /**
   * Gets a suggestion for resolving the error.
   * 
   * @param throwable The exception to analyze
   * @return A suggestion for resolving the error, or null if no specific suggestion
   */
  public static String getSuggestion(Throwable throwable) {
    if (throwable == null) {
      return null;
    }
    
    // Unwrap CompletionException
    if (throwable instanceof CompletionException && throwable.getCause() != null) {
      throwable = throwable.getCause();
    }
    
    if (throwable instanceof UnknownHostException) {
      return "Check your internet connection and verify the base URL is correct.";
    }
    
    if (throwable instanceof ConnectException) {
      return "Verify the base URL is correct and the service is running.";
    }
    
    if (throwable instanceof SocketTimeoutException) {
      return "Try again in a few moments. If the problem persists, the service may be overloaded.";
    }
    
    if (throwable instanceof IOException) {
      String message = throwable.getMessage();
      if (message != null) {
        if (message.contains("401")) {
          return "Check your API key in the settings.";
        }
        if (message.contains("403")) {
          return "Verify your API key has permission to use the selected model.";
        }
        if (message.contains("404")) {
          return "Check the model name in the settings.";
        }
        if (message.contains("429")) {
          return "Wait a few minutes before trying again.";
        }
        if (message.contains("500") || message.contains("502") || 
            message.contains("503") || message.contains("504")) {
          return "The service is temporarily unavailable. Try again later.";
        }
      }
    }
    
    if (isConfigurationError(throwable)) {
      return "Check your AI provider settings.";
    }
    
    return null;
  }
}
