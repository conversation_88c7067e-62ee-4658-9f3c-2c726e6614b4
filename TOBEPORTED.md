ai\
├── AIAction.java
├── AIChatDialog.java
├── AIClient.java
├── AIClientFactory.java
├── AIConfigurationManager.java
├── AIConfigValidator.java
├── AIErrorHandler.java
├── AIIntegration.java
├── AIModelParameters.java
├── AIPlugin.java
├── AIPluginValidator.java
├── AIProviderConfig.java
├── AIProviderPreset.java
├── AIResourceBundle.java
├── AISettingsDialog.java
├── ApplicationPlugin.properties
├── FloorPlanDataExtractor.java
├── OpenAICompatibleClient.java
├── PrivacyManager.java
├── SecureConfigStorage.java
└── ValidationResult.java

<file path="AIAction.java">
/*
 * AIAction.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.UserPreferences;
import com.eteks.sweethome3d.plugin.PluginAction;
import com.eteks.sweethome3d.viewcontroller.HomeController;
import javax.swing.SwingUtilities;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.lang.ref.WeakReference;

/**
 * Action that opens the AI analysis dialog when triggered.
 * This action is added to the Sweet Home 3D toolbar and menu.
 * Uses SweetHome 3D's internationalization system for proper localization.
 *
 * <AUTHOR> Kpassegna
 */
public class AIAction extends PluginAction {
  private final HomeController homeController;
  private final AIIntegration aiIntegration;
  private final UserPreferences preferences;

  /**
   * Creates a new AI action.
   *
   * @param homeController The home controller
   */
  public AIAction(HomeController homeController) {
    this.homeController = homeController;
    this.preferences = homeController.getUserPreferences();
    this.aiIntegration = new AIIntegration(this.preferences);

    // Initialize action properties using SweetHome 3D's approach
    updateActionProperties();

    // Listen for language changes to update action properties
    this.preferences.addPropertyChangeListener(UserPreferences.Property.LANGUAGE,
        new LanguageChangeListener(this));
  }
  
  /**
   * Preferences property listener bound to this action with a weak reference to avoid
   * strong link between preferences and this action.
   */
  private static class LanguageChangeListener implements PropertyChangeListener {
    private final WeakReference<AIAction> aiAction;

    public LanguageChangeListener(AIAction aiAction) {
      this.aiAction = new WeakReference<AIAction>(aiAction);
    }

    public void propertyChange(PropertyChangeEvent ev) {
      // If action was garbage collected, remove this listener from preferences
      AIAction aiAction = this.aiAction.get();
      if (aiAction == null) {
        ((UserPreferences)ev.getSource()).removePropertyChangeListener(
            UserPreferences.Property.LANGUAGE, this);
      } else {
        aiAction.updateActionProperties();
      }
    }
  }

  /**
   * Updates action properties using localized strings from resource bundle.
   */
  private void updateActionProperties() {
    try {
      // Use UserPreferences to get localized strings
      String name = preferences.getLocalizedString(AIAction.class, "AIAction.Name");
      String shortDescription = preferences.getLocalizedString(AIAction.class, "AIAction.ShortDescription");
      String menu = preferences.getLocalizedString(AIAction.class, "AIAction.Menu");

      putPropertyValue(Property.NAME, name);
      putPropertyValue(Property.SHORT_DESCRIPTION, shortDescription);
      putPropertyValue(Property.MENU, menu);
      putPropertyValue(Property.TOOL_BAR, true);
      putPropertyValue(Property.ENABLED, true);

      // Set icon (placeholder - actual icon file should be added to resources)
      // putPropertyValue(Property.SMALL_ICON, new URLContent(getClass().getResource("resources/ai-icon-16.png")));
    } catch (IllegalArgumentException ex) {
      // Fallback to English if localized strings are not found
      putPropertyValue(Property.NAME, "AI Analysis");
      putPropertyValue(Property.SHORT_DESCRIPTION, "Analyze floor plan with AI");
      putPropertyValue(Property.MENU, "Tools");
      putPropertyValue(Property.TOOL_BAR, true);
      putPropertyValue(Property.ENABLED, true);
    }
  }

  /**
   * Executes the AI action.
   */
  @Override
  public void execute() {
    // Check if AI is configured
    if (!aiIntegration.isConfigured()) {
      showConfigurationDialog();
      return;
    }

    // Open AI chat dialog
    openAIChatDialog();
  }
  
  /**
   * Shows the AI configuration dialog.
   */
  private void showConfigurationDialog() {
    SwingUtilities.invokeLater(() -> {
      AISettingsDialog dialog = new AISettingsDialog(homeController, aiIntegration);
      dialog.setVisible(true);
      
      // If configuration was saved, try to execute the action again
      if (dialog.isConfigurationSaved()) {
        aiIntegration.reconfigure();
        execute();
      }
    });
  }
  
  /**
   * Opens the AI chat dialog for analysis.
   */
  private void openAIChatDialog() {
    SwingUtilities.invokeLater(() -> {
      Home home = homeController.getHome();
      if (home == null) {
        return;
      }
      
      AIChatDialog chatDialog = new AIChatDialog(homeController, aiIntegration);
      chatDialog.setVisible(true);
    });
  }
  
  /**
   * Returns the home controller.
   */
  protected HomeController getHomeController() {
    return homeController;
  }
  
  /**
   * Returns the AI integration instance.
   */
  protected AIIntegration getAIIntegration() {
    return aiIntegration;
  }
}

</file>
<file path="AIChatDialog.java">
/*
 * AIChatDialog.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.UserPreferences;
import com.eteks.sweethome3d.viewcontroller.HomeController;
import javax.swing.*;
import javax.swing.text.BadLocationException;
import javax.swing.text.Style;
import javax.swing.text.StyleConstants;
import javax.swing.text.StyledDocument;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Dialog for displaying AI floor plan analysis results.
 * Provides a chat-like interface for AI interactions.
 * Uses SweetHome 3D's internationalization system for proper localization.
 *
 * <AUTHOR> Kpassegna
 */
public class AIChatDialog extends JDialog {
  private final HomeController homeController;
  private final AIIntegration aiIntegration;
  private final Home home;
  private final UserPreferences preferences;
  
  // UI Components
  private JTextPane chatArea;
  private JTextField inputField;
  private JButton sendButton;
  private JButton newAnalysisButton;
  private JButton settingsButton;
  private JProgressBar analysisProgressBar;
  private JScrollPane chatScrollPane;
  
  // Styles for chat display
  private Style userStyle;
  private Style aiStyle;
  private Style systemStyle;
  
  /**
   * Creates a new AI chat dialog.
   */
  public AIChatDialog(HomeController homeController, AIIntegration aiIntegration) {
    this.homeController = homeController;
    this.aiIntegration = aiIntegration;
    this.home = homeController.getHome();
    this.preferences = homeController.getUserPreferences();

    // Set localized title
    String title = getLocalizedString("AIChatDialog.title", "AI Floor Plan Analysis");
    setTitle(title);
    setModal(false);

    initializeComponents();
    setupStyles();
    layoutComponents();
    setupEventHandlers();

    setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    setSize(600, 500);
    setLocationRelativeTo(SwingUtilities.getWindowAncestor(homeController.getView()));

    // Listen for language changes to update UI
    this.preferences.addPropertyChangeListener(UserPreferences.Property.LANGUAGE,
        new LanguageChangeListener(this));

    // Start initial analysis
    startInitialAnalysis();
  }
  
  /**
   * Preferences property listener bound to this dialog with a weak reference to avoid
   * strong link between preferences and this dialog.
   */
  private static class LanguageChangeListener implements PropertyChangeListener {
    private final WeakReference<AIChatDialog> dialog;

    public LanguageChangeListener(AIChatDialog dialog) {
      this.dialog = new WeakReference<AIChatDialog>(dialog);
    }

    public void propertyChange(PropertyChangeEvent ev) {
      // If dialog was garbage collected, remove this listener from preferences
      AIChatDialog dialog = this.dialog.get();
      if (dialog == null) {
        ((UserPreferences)ev.getSource()).removePropertyChangeListener(
            UserPreferences.Property.LANGUAGE, this);
      } else {
        dialog.updateLocalizedStrings();
      }
    }
  }

  /**
   * Returns a localized string for the given key.
   */
  private String getLocalizedString(String key, String defaultValue) {
    try {
      return preferences.getLocalizedString(AIChatDialog.class, key);
    } catch (IllegalArgumentException ex) {
      return defaultValue;
    }
  }

  /**
   * Updates all localized strings in the UI.
   */
  private void updateLocalizedStrings() {
    setTitle(getLocalizedString("AIChatDialog.title", "AI Floor Plan Analysis"));
    sendButton.setText(getLocalizedString("button.send", "Send"));
    newAnalysisButton.setText(getLocalizedString("button.newAnalysis", "New Analysis"));
    settingsButton.setText(getLocalizedString("button.settings", "Settings"));
    analysisProgressBar.setString(getLocalizedString("message.analyzing", "Analyzing floor plan..."));
  }

  /**
   * Initializes the UI components.
   */
  private void initializeComponents() {
    // Chat area
    chatArea = new JTextPane();
    chatArea.setEditable(false);
    chatArea.setBackground(Color.WHITE);
    chatScrollPane = new JScrollPane(chatArea);
    chatScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
    chatScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);

    // Input field and send button
    inputField = new JTextField();
    sendButton = new JButton(getLocalizedString("button.send", "Send"));

    // Action buttons
    newAnalysisButton = new JButton(getLocalizedString("button.newAnalysis", "New Analysis"));
    settingsButton = new JButton(getLocalizedString("button.settings", "Settings"));

    // Progress bar
    analysisProgressBar = new JProgressBar();
    analysisProgressBar.setIndeterminate(true);
    analysisProgressBar.setString(getLocalizedString("message.analyzing", "Analyzing floor plan..."));
    analysisProgressBar.setStringPainted(true);
    analysisProgressBar.setVisible(false);
  }
  
  /**
   * Sets up text styles for the chat area.
   */
  private void setupStyles() {
    StyledDocument doc = chatArea.getStyledDocument();
    
    // User style (right-aligned, blue)
    userStyle = doc.addStyle("user", null);
    StyleConstants.setForeground(userStyle, new Color(0, 100, 200));
    StyleConstants.setBold(userStyle, true);
    StyleConstants.setAlignment(userStyle, StyleConstants.ALIGN_RIGHT);
    
    // AI style (left-aligned, dark green)
    aiStyle = doc.addStyle("ai", null);
    StyleConstants.setForeground(aiStyle, new Color(0, 120, 0));
    StyleConstants.setAlignment(aiStyle, StyleConstants.ALIGN_LEFT);
    
    // System style (centered, gray)
    systemStyle = doc.addStyle("system", null);
    StyleConstants.setForeground(systemStyle, Color.GRAY);
    StyleConstants.setItalic(systemStyle, true);
    StyleConstants.setAlignment(systemStyle, StyleConstants.ALIGN_CENTER);
  }
  
  /**
   * Layouts the components in the dialog.
   */
  private void layoutComponents() {
    setLayout(new BorderLayout());
    
    // Top panel with action buttons
    JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
    topPanel.add(newAnalysisButton);
    topPanel.add(settingsButton);
    add(topPanel, BorderLayout.NORTH);
    
    // Center panel with chat area
    add(chatScrollPane, BorderLayout.CENTER);
    
    // Bottom panel with input and progress
    JPanel bottomPanel = new JPanel(new BorderLayout());
    
    // Input panel
    JPanel inputPanel = new JPanel(new BorderLayout());
    inputPanel.add(inputField, BorderLayout.CENTER);
    inputPanel.add(sendButton, BorderLayout.EAST);
    inputPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
    
    bottomPanel.add(inputPanel, BorderLayout.CENTER);
    bottomPanel.add(analysisProgressBar, BorderLayout.SOUTH);
    
    add(bottomPanel, BorderLayout.SOUTH);
  }
  
  /**
   * Sets up event handlers for the components.
   */
  private void setupEventHandlers() {
    sendButton.addActionListener(this::onSendMessage);
    inputField.addActionListener(this::onSendMessage);
    newAnalysisButton.addActionListener(this::onNewAnalysis);
    settingsButton.addActionListener(this::onSettings);
  }
  
  /**
   * Starts the initial floor plan analysis.
   */
  private void startInitialAnalysis() {
    if (!aiIntegration.isConfigured()) {
      showConfigurationPrompt();
      return;
    }
    
    appendToChat("System", "Starting floor plan analysis...", systemStyle);
    
    analysisProgressBar.setVisible(true);
    sendButton.setEnabled(false);
    newAnalysisButton.setEnabled(false);
    
    aiIntegration.analyzeHome(home)
        .thenAccept(this::displayAnalysis)
        .exceptionally(this::handleAnalysisError);
  }
  
  /**
   * Shows a configuration prompt if AI is not configured.
   */
  private void showConfigurationPrompt() {
    appendToChat("System", "AI is not configured. Please click Settings to configure your AI provider.", systemStyle);
    sendButton.setEnabled(false);
    newAnalysisButton.setEnabled(false);
  }
  
  /**
   * Displays the AI analysis result.
   */
  private void displayAnalysis(String analysis) {
    SwingUtilities.invokeLater(() -> {
      appendToChat("AI Assistant", analysis, aiStyle);
      analysisProgressBar.setVisible(false);
      sendButton.setEnabled(true);
      newAnalysisButton.setEnabled(true);
    });
  }
  
  /**
   * Handles analysis errors.
   */
  private Void handleAnalysisError(Throwable error) {
    SwingUtilities.invokeLater(() -> {
      new AIErrorHandler().handleAnalysisError(error, this);
      appendToChat("System", "Analysis failed. Please check your AI settings and try again.", systemStyle);
      analysisProgressBar.setVisible(false);
      sendButton.setEnabled(true);
      newAnalysisButton.setEnabled(true);
    });
    return null;
  }
  
  /**
   * Appends a message to the chat area.
   */
  private void appendToChat(String sender, String message, Style style) {
    try {
      StyledDocument doc = chatArea.getStyledDocument();
      
      // Add timestamp and sender
      String timestamp = new SimpleDateFormat("HH:mm").format(new Date());
      String header = String.format("[%s] %s:\n", timestamp, sender);
      
      doc.insertString(doc.getLength(), header, style);
      doc.insertString(doc.getLength(), message + "\n\n", style);
      
      // Scroll to bottom
      chatArea.setCaretPosition(doc.getLength());
      
    } catch (BadLocationException e) {
      // Handle error silently
    }
  }
  
  /**
   * Handles send message action.
   */
  private void onSendMessage(ActionEvent e) {
    String message = inputField.getText().trim();
    if (message.isEmpty() || !aiIntegration.isConfigured()) {
      return;
    }
    
    // Display user message
    appendToChat("You", message, userStyle);
    inputField.setText("");
    
    // Send to AI (for follow-up questions)
    sendButton.setEnabled(false);
    analysisProgressBar.setString(getLocalizedString("message.processingQuestion", "Processing question..."));
    analysisProgressBar.setVisible(true);
    
    aiIntegration.askQuestion(home, message)
        .thenAccept(this::displayAnalysis)
        .exceptionally(this::handleAnalysisError);
  }
  
  /**
   * Handles new analysis action.
   */
  private void onNewAnalysis(ActionEvent e) {
    // Clear chat area
    chatArea.setText("");
    
    // Start new analysis
    startInitialAnalysis();
  }
  
  /**
   * Handles settings action.
   */
  private void onSettings(ActionEvent e) {
    AISettingsDialog settingsDialog = new AISettingsDialog(homeController, aiIntegration);
    settingsDialog.setVisible(true);
    
    if (settingsDialog.isConfigurationSaved()) {
      aiIntegration.reconfigure();
      appendToChat("System", "AI configuration updated.", systemStyle);
      
      // Enable controls if now configured
      if (aiIntegration.isConfigured()) {
        sendButton.setEnabled(true);
        newAnalysisButton.setEnabled(true);
      }
    }
  }
}

</file>
<file path="AIClient.java">
/*
 * AIClient.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Interface for AI clients that can analyze floor plans.
 * Supports OpenAI-compatible endpoints using the Chat Completions API.
 * 
 * <AUTHOR> Kpassegna
 */
public interface AIClient {
  
  /**
   * Analyzes a floor plan using the configured AI model.
   * 
   * @param floorPlanData JSON representation of the floor plan data
   * @param prompt The analysis prompt to send to the AI
   * @return A CompletableFuture containing the AI's analysis response
   */
  CompletableFuture<String> analyzeFloorPlan(String floorPlanData, String prompt);
  
  /**
   * Tests the connection to the AI service.
   * 
   * @return true if the connection is successful, false otherwise
   */
  boolean testConnection();
  
  /**
   * Retrieves the list of available models from the provider.
   * 
   * @return A list of available model names
   */
  List<String> getAvailableModels();
  
  /**
   * Closes the client and releases any resources.
   */
  void close();
}

</file>
<file path="AIClientFactory.java">
/*
 * AIClientFactory.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

/**
 * Factory for creating AI clients based on provider configuration.
 * 
 * <AUTHOR> Kpassegna
 */
public class AIClientFactory {
  
  /**
   * Creates an AI client for the given configuration.
   * 
   * @param config The provider configuration
   * @return A configured AI client
   * @throws IllegalArgumentException if the configuration is invalid
   */
  public static AIClient createClient(AIProviderConfig config) {
    validateConfig(config);
    return new OpenAICompatibleClient(config);
  }
  
  /**
   * Validates the provider configuration.
   * 
   * @param config The configuration to validate
   * @throws IllegalArgumentException if the configuration is invalid
   */
  private static void validateConfig(AIProviderConfig config) {
    if (config == null) {
      throw new IllegalArgumentException("Configuration cannot be null");
    }
    
    if (config.getBaseUrl() == null || config.getBaseUrl().trim().isEmpty()) {
      throw new IllegalArgumentException("Base URL is required");
    }
    
    if (config.getModel() == null || config.getModel().trim().isEmpty()) {
      throw new IllegalArgumentException("Model is required");
    }
    
    // API key validation is more lenient since local providers might not need it
    String baseUrl = config.getBaseUrl().toLowerCase();
    boolean isLocal = baseUrl.contains("localhost") || 
                     baseUrl.contains("127.0.0.1") || 
                     baseUrl.contains("0.0.0.0");
    
    if (!isLocal && (config.getApiKey() == null || config.getApiKey().trim().isEmpty())) {
      throw new IllegalArgumentException("API Key is required for remote providers");
    }
  }
}

</file>
<file path="AIConfigurationManager.java">
/*
 * AIConfigurationManager.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.UserPreferences;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.prefs.Preferences;

/**
 * Manages AI provider configuration storage and retrieval using Sweet Home 3D's preferences system.
 * Handles secure storage of API keys and provider settings.
 * 
 * <AUTHOR> Kpassegna
 */
public class AIConfigurationManager {
  private static final String AI_PROVIDER_NAME = "aiProviderName";
  private static final String AI_BASE_URL = "aiBaseUrl";
  private static final String AI_API_KEY = "aiApiKey";
  private static final String AI_MODEL = "aiModel";
  private static final String AI_CUSTOM_HEADERS = "aiCustomHeaders";
  private static final String AI_TEMPERATURE = "aiTemperature";
  private static final String AI_MAX_TOKENS = "aiMaxTokens";
  private static final String AI_TOP_P = "aiTopP";
  private static final String AI_FREQUENCY_PENALTY = "aiFrequencyPenalty";
  private static final String AI_PRESENCE_PENALTY = "aiPresencePenalty";
  
  private final Preferences preferences;
  private final SecureConfigStorage secureStorage;
  
  /**
   * Creates a new configuration manager.
   */
  public AIConfigurationManager() {
    this.preferences = Preferences.userNodeForPackage(AIConfigurationManager.class);
    this.secureStorage = new SecureConfigStorage();
  }
  
  /**
   * Saves the AI provider configuration.
   */
  public void saveConfiguration(AIProviderConfig config) {
    if (config == null) {
      return;
    }
    
    preferences.put(AI_PROVIDER_NAME, config.getProviderName() != null ? config.getProviderName() : "");
    preferences.put(AI_BASE_URL, config.getBaseUrl() != null ? config.getBaseUrl() : "");
    preferences.put(AI_MODEL, config.getModel() != null ? config.getModel() : "");
    
    // Store API key securely
    if (config.getApiKey() != null && !config.getApiKey().isEmpty()) {
      secureStorage.storeApiKey(config.getApiKey());
    }
    
    // Store custom headers as a simple string format
    if (config.getCustomHeaders() != null && !config.getCustomHeaders().isEmpty()) {
      StringBuilder headersStr = new StringBuilder();
      for (Map.Entry<String, String> entry : config.getCustomHeaders().entrySet()) {
        if (headersStr.length() > 0) {
          headersStr.append(";");
        }
        headersStr.append(entry.getKey()).append("=").append(entry.getValue());
      }
      preferences.put(AI_CUSTOM_HEADERS, headersStr.toString());
    } else {
      preferences.remove(AI_CUSTOM_HEADERS);
    }
    
    // Store model parameters
    AIModelParameters params = config.getModelParams();
    if (params != null) {
      preferences.putDouble(AI_TEMPERATURE, params.getTemperature());
      preferences.putInt(AI_MAX_TOKENS, params.getMaxTokens());
      preferences.putDouble(AI_TOP_P, params.getTopP());
      preferences.putInt(AI_FREQUENCY_PENALTY, params.getFrequencyPenalty());
      preferences.putInt(AI_PRESENCE_PENALTY, params.getPresencePenalty());
    }
  }
  
  /**
   * Loads the AI provider configuration.
   */
  public AIProviderConfig loadConfiguration() {
    String providerName = preferences.get(AI_PROVIDER_NAME, "");
    String baseUrl = preferences.get(AI_BASE_URL, "");
    String model = preferences.get(AI_MODEL, "");
    
    if (baseUrl.isEmpty()) {
      return null; // No configuration saved
    }
    
    AIProviderConfig config = new AIProviderConfig();
    config.setProviderName(providerName);
    config.setBaseUrl(baseUrl);
    config.setModel(model);
    
    // Load API key securely
    String apiKey = secureStorage.retrieveApiKey();
    if (apiKey != null && !apiKey.isEmpty()) {
      config.setApiKey(apiKey);
    }
    
    // Load custom headers
    String headersStr = preferences.get(AI_CUSTOM_HEADERS, "");
    if (!headersStr.isEmpty()) {
      Map<String, String> customHeaders = new HashMap<>();
      String[] headerPairs = headersStr.split(";");
      for (String pair : headerPairs) {
        String[] keyValue = pair.split("=", 2);
        if (keyValue.length == 2) {
          customHeaders.put(keyValue[0], keyValue[1]);
        }
      }
      config.setCustomHeaders(customHeaders);
    }
    
    // Load model parameters
    AIModelParameters params = new AIModelParameters();
    params.setTemperature(preferences.getDouble(AI_TEMPERATURE, 0.7));
    params.setMaxTokens(preferences.getInt(AI_MAX_TOKENS, 2048));
    params.setTopP(preferences.getDouble(AI_TOP_P, 1.0));
    params.setFrequencyPenalty(preferences.getInt(AI_FREQUENCY_PENALTY, 0));
    params.setPresencePenalty(preferences.getInt(AI_PRESENCE_PENALTY, 0));
    config.setModelParams(params);
    
    return config;
  }
  
  /**
   * Returns preset configurations for popular providers.
   */
  public List<AIProviderConfig> getPresetConfigurations() {
    List<AIProviderConfig> presets = new ArrayList<>();
    for (AIProviderPreset preset : AIProviderPreset.values()) {
      if (preset != AIProviderPreset.CUSTOM) {
        presets.add(preset.createConfig());
      }
    }
    return presets;
  }
  
  /**
   * Clears all stored configuration.
   */
  public void clearConfiguration() {
    preferences.remove(AI_PROVIDER_NAME);
    preferences.remove(AI_BASE_URL);
    preferences.remove(AI_MODEL);
    preferences.remove(AI_CUSTOM_HEADERS);
    preferences.remove(AI_TEMPERATURE);
    preferences.remove(AI_MAX_TOKENS);
    preferences.remove(AI_TOP_P);
    preferences.remove(AI_FREQUENCY_PENALTY);
    preferences.remove(AI_PRESENCE_PENALTY);
    
    secureStorage.clearApiKey();
  }
  
  /**
   * Returns whether a configuration is currently saved.
   */
  public boolean hasConfiguration() {
    return !preferences.get(AI_BASE_URL, "").isEmpty();
  }
}

</file>
<file path="AIConfigValidator.java">
/*
 * AIConfigValidator.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * Validates AI provider configurations.
 * 
 * <AUTHOR> Kpassegna
 */
public class AIConfigValidator {
  
  /**
   * Validates an AI provider configuration.
   */
  public ValidationResult validate(AIProviderConfig config) {
    List<String> errors = new ArrayList<>();
    
    if (config == null) {
      errors.add("Configuration cannot be null");
      return new ValidationResult(false, errors);
    }
    
    // Validate base URL
    if (isBlank(config.getBaseUrl())) {
      errors.add("Base URL is required");
    } else if (!isValidUrl(config.getBaseUrl())) {
      errors.add("Base URL must be a valid URL");
    }
    
    // Validate API key (required for most providers)
    if (isBlank(config.getApiKey())) {
      // Check if this is a local provider that might not need an API key
      String baseUrl = config.getBaseUrl();
      if (baseUrl != null && !isLocalUrl(baseUrl)) {
        errors.add("API Key is required for remote providers");
      }
    }
    
    // Validate model selection
    if (isBlank(config.getModel())) {
      errors.add("Model selection is required");
    }
    
    // Validate provider name
    if (isBlank(config.getProviderName())) {
      errors.add("Provider name is required");
    }
    
    return new ValidationResult(errors.isEmpty(), errors);
  }
  
  /**
   * Checks if a string is blank (null, empty, or whitespace only).
   */
  private boolean isBlank(String str) {
    return str == null || str.trim().isEmpty();
  }
  
  /**
   * Validates if a string is a valid URL.
   */
  private boolean isValidUrl(String urlString) {
    try {
      new URL(urlString);
      return true;
    } catch (MalformedURLException e) {
      return false;
    }
  }
  
  /**
   * Checks if a URL appears to be a local/localhost URL.
   */
  private boolean isLocalUrl(String urlString) {
    if (urlString == null) {
      return false;
    }
    
    String lowerUrl = urlString.toLowerCase();
    return lowerUrl.contains("localhost") || 
           lowerUrl.contains("127.0.0.1") || 
           lowerUrl.contains("0.0.0.0") ||
           lowerUrl.matches(".*://192\\.168\\..*") ||
           lowerUrl.matches(".*://10\\..*") ||
           lowerUrl.matches(".*://172\\.(1[6-9]|2[0-9]|3[01])\\..*");
  }
}

</file>
<file path="AIErrorHandler.java">
/*
 * AIErrorHandler.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import javax.swing.JOptionPane;
import java.awt.Component;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

/**
 * Handles AI-related errors and provides user-friendly error messages.
 * 
 * <AUTHOR> Kpassegna
 */
public class AIErrorHandler {
  
  /**
   * Handles analysis errors and displays appropriate messages to the user.
   * 
   * @param error The error that occurred
   * @param parentComponent The parent component for the error dialog
   */
  public void handleAnalysisError(Throwable error, Component parentComponent) {
    String message;
    String title = "AI Analysis Error";
    
    if (error instanceof IllegalStateException) {
      message = "AI service is not configured. Please check your AI settings.";
    } else if (error.getCause() instanceof ConnectException || error instanceof ConnectException) {
      message = "Cannot connect to AI service. Please check your network connection and AI settings.";
    } else if (error.getCause() instanceof UnknownHostException || error instanceof UnknownHostException) {
      message = "Cannot resolve AI service hostname. Please check your network connection and AI settings.";
    } else if (error.getCause() instanceof SocketTimeoutException || error instanceof SocketTimeoutException) {
      message = "Request timed out. The AI service may be overloaded. Please try again later.";
    } else if (error.getMessage() != null) {
      String errorMsg = error.getMessage().toLowerCase();
      if (errorMsg.contains("401") || errorMsg.contains("unauthorized")) {
        message = "Authentication failed. Please check your API key in AI settings.";
      } else if (errorMsg.contains("403") || errorMsg.contains("forbidden")) {
        message = "Access denied. Please check your API key permissions in AI settings.";
      } else if (errorMsg.contains("429") || errorMsg.contains("rate limit")) {
        message = "Rate limit exceeded. Please try again later.";
      } else if (errorMsg.contains("404") || errorMsg.contains("not found")) {
        message = "AI service endpoint not found. Please check your base URL in AI settings.";
      } else if (errorMsg.contains("500") || errorMsg.contains("internal server error")) {
        message = "AI service is experiencing issues. Please try again later.";
      } else if (errorMsg.contains("502") || errorMsg.contains("bad gateway")) {
        message = "AI service is temporarily unavailable. Please try again later.";
      } else if (errorMsg.contains("503") || errorMsg.contains("service unavailable")) {
        message = "AI service is temporarily unavailable. Please try again later.";
      } else {
        message = "An unexpected error occurred: " + error.getMessage();
      }
    } else {
      message = "An unexpected error occurred. Please check your AI settings and try again.";
    }
    
    JOptionPane.showMessageDialog(parentComponent, message, title, JOptionPane.ERROR_MESSAGE);
  }
  
  /**
   * Handles configuration errors and displays appropriate messages to the user.
   * 
   * @param error The error that occurred
   * @param parentComponent The parent component for the error dialog
   */
  public void handleConfigurationError(Throwable error, Component parentComponent) {
    String message;
    String title = "AI Configuration Error";
    
    if (error instanceof IllegalArgumentException) {
      message = "Invalid configuration: " + error.getMessage();
    } else {
      message = "Configuration error: " + (error.getMessage() != null ? error.getMessage() : "Unknown error");
    }
    
    JOptionPane.showMessageDialog(parentComponent, message, title, JOptionPane.ERROR_MESSAGE);
  }
  
  /**
   * Handles connection test errors and returns a user-friendly message.
   * 
   * @param error The error that occurred during connection testing
   * @return A user-friendly error message
   */
  public String getConnectionTestErrorMessage(Throwable error) {
    if (error instanceof ConnectException) {
      return "Cannot connect to the AI service. Please check the base URL and your network connection.";
    } else if (error instanceof UnknownHostException) {
      return "Cannot resolve the AI service hostname. Please check the base URL.";
    } else if (error instanceof SocketTimeoutException) {
      return "Connection timed out. The AI service may be unavailable.";
    } else if (error.getMessage() != null) {
      String errorMsg = error.getMessage().toLowerCase();
      if (errorMsg.contains("401") || errorMsg.contains("unauthorized")) {
        return "Authentication failed. Please check your API key.";
      } else if (errorMsg.contains("403") || errorMsg.contains("forbidden")) {
        return "Access denied. Please check your API key permissions.";
      } else if (errorMsg.contains("404") || errorMsg.contains("not found")) {
        return "AI service endpoint not found. Please check the base URL.";
      } else {
        return "Connection failed: " + error.getMessage();
      }
    } else {
      return "Connection failed for an unknown reason.";
    }
  }
}

</file>
<file path="AIIntegration.java">
/*
 * AIIntegration.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.HomePieceOfFurniture;
import com.eteks.sweethome3d.model.Wall;
import com.eteks.sweethome3d.model.Room;
import com.eteks.sweethome3d.model.Level;
import com.eteks.sweethome3d.model.Compass;
import com.eteks.sweethome3d.model.HomeEnvironment;
import com.eteks.sweethome3d.model.UserPreferences;
import java.util.concurrent.CompletableFuture;

/**
 * Main integration class that handles data extraction from Sweet Home 3D
 * and communication with AI services for floor plan analysis.
 *
 * <AUTHOR> Kpassegna
 */
public class AIIntegration {
  private final AIConfigurationManager configManager;
  private final PrivacyManager privacyManager;
  private final UserPreferences preferences;
  private AIClient aiClient;

  /**
   * Creates a new AI integration instance.
   *
   * @param preferences user preferences for accessing localized strings
   */
  public AIIntegration(UserPreferences preferences) {
    this.preferences = preferences;
    this.configManager = new AIConfigurationManager();
    this.privacyManager = new PrivacyManager();
    initializeClient();
  }
  
  /**
   * Initializes the AI client from saved configuration.
   */
  private void initializeClient() {
    AIProviderConfig config = configManager.loadConfiguration();
    if (config != null && isConfigurationValid(config)) {
      try {
        this.aiClient = AIClientFactory.createClient(config);
      } catch (Exception e) {
        // Client initialization failed, will be null
        this.aiClient = null;
      }
    }
  }
  
  /**
   * Analyzes a home's floor plan using AI.
   *
   * @param home The home to analyze
   * @return A CompletableFuture containing the AI analysis
   */
  public CompletableFuture<String> analyzeHome(Home home) {
    if (aiClient == null) {
      throw new IllegalStateException("AI client not configured. Please configure AI settings first.");
    }

    String floorPlanData = extractFloorPlanData(home);
    String analysisPrompt = buildAnalysisPrompt();

    return aiClient.analyzeFloorPlan(floorPlanData, analysisPrompt);
  }

  /**
   * Asks a follow-up question about the floor plan.
   *
   * @param home The home to analyze
   * @param question The follow-up question
   * @return A CompletableFuture containing the AI response
   */
  public CompletableFuture<String> askQuestion(Home home, String question) {
    if (aiClient == null) {
      throw new IllegalStateException("AI client not configured. Please configure AI settings first.");
    }

    String floorPlanData = extractFloorPlanData(home);
    return aiClient.analyzeFloorPlan(floorPlanData, question);
  }
  
  /**
   * Extracts floor plan data from a Home object and serializes it to JSON.
   *
   * @param home The home to extract data from
   * @return JSON representation of the floor plan data
   */
  public String extractFloorPlanData(Home home) {
    FloorPlanDataExtractor extractor = new FloorPlanDataExtractor();
    String rawData = extractor.extractToJson(home);

    // Apply privacy settings
    boolean excludePersonalInfo = privacyManager.shouldExcludePersonalInfo();
    return privacyManager.sanitizeFloorPlanData(rawData, !excludePersonalInfo);
  }
  
  /**
   * Builds the analysis prompt for the AI using localized strings.
   *
   * @return The analysis prompt
   */
  private String buildAnalysisPrompt() {
    try {
      return preferences.getLocalizedString(AIIntegration.class, "analysis.prompt");
    } catch (IllegalArgumentException ex) {
      // Fallback to English if localized prompt is not found
      return "Please analyze this floor plan and provide comprehensive insights including:\n" +
             "1. Layout efficiency and space utilization\n" +
             "2. Traffic flow and circulation patterns\n" +
             "3. Natural lighting and ventilation opportunities\n" +
             "4. Accessibility considerations\n" +
             "5. Functional relationships between spaces\n" +
             "6. Suggestions for improvement\n" +
             "7. Compliance with common building standards\n" +
             "8. Energy efficiency considerations\n\n" +
             "Please provide specific, actionable recommendations that would enhance " +
             "the functionality, comfort, and aesthetic appeal of this space.";
    }
  }
  
  /**
   * Checks if the AI client is properly configured.
   * 
   * @return true if configured, false otherwise
   */
  public boolean isConfigured() {
    return aiClient != null;
  }
  
  /**
   * Reconfigures the AI client with current settings.
   */
  public void reconfigure() {
    if (aiClient != null) {
      aiClient.close();
    }
    initializeClient();
  }
  
  /**
   * Tests the connection to the AI service.
   * 
   * @return true if connection is successful, false otherwise
   */
  public boolean testConnection() {
    return aiClient != null && aiClient.testConnection();
  }
  
  /**
   * Gets the current configuration.
   * 
   * @return The current AI provider configuration, or null if not configured
   */
  public AIProviderConfig getCurrentConfiguration() {
    return configManager.loadConfiguration();
  }
  
  /**
   * Saves a new configuration.
   * 
   * @param config The configuration to save
   */
  public void saveConfiguration(AIProviderConfig config) {
    configManager.saveConfiguration(config);
    reconfigure();
  }
  
  /**
   * Validates a configuration.
   * 
   * @param config The configuration to validate
   * @return true if valid, false otherwise
   */
  private boolean isConfigurationValid(AIProviderConfig config) {
    if (config == null) {
      return false;
    }
    
    AIConfigValidator validator = new AIConfigValidator();
    ValidationResult result = validator.validate(config);
    return result.isValid();
  }
  
  /**
   * Gets the privacy manager.
   *
   * @return The privacy manager instance
   */
  public PrivacyManager getPrivacyManager() {
    return privacyManager;
  }

  /**
   * Closes the AI integration and releases resources.
   */
  public void close() {
    if (aiClient != null) {
      aiClient.close();
      aiClient = null;
    }
  }
}

</file>
<file path="AIModelParameters.java">
/*
 * AIModelParameters.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.io.Serializable;

/**
 * Model-specific parameters for AI requests.
 * These parameters control the behavior and output characteristics of AI models.
 * 
 * <AUTHOR> Kpassegna
 */
public class AIModelParameters implements Serializable {
  private static final long serialVersionUID = 1L;
  
  private double temperature = 0.7;
  private int maxTokens = 2048;
  private double topP = 1.0;
  private int frequencyPenalty = 0;
  private int presencePenalty = 0;
  
  /**
   * Creates default model parameters.
   */
  public AIModelParameters() {
    // Default values are set in field declarations
  }
  
  /**
   * Creates model parameters with specified values.
   */
  public AIModelParameters(double temperature, int maxTokens, double topP, 
                          int frequencyPenalty, int presencePenalty) {
    this.temperature = temperature;
    this.maxTokens = maxTokens;
    this.topP = topP;
    this.frequencyPenalty = frequencyPenalty;
    this.presencePenalty = presencePenalty;
  }
  
  /**
   * Returns the temperature parameter (0.0 to 2.0).
   * Higher values make output more random, lower values more focused.
   */
  public double getTemperature() {
    return temperature;
  }
  
  /**
   * Sets the temperature parameter (0.0 to 2.0).
   */
  public void setTemperature(double temperature) {
    this.temperature = Math.max(0.0, Math.min(2.0, temperature));
  }
  
  /**
   * Returns the maximum number of tokens to generate.
   */
  public int getMaxTokens() {
    return maxTokens;
  }
  
  /**
   * Sets the maximum number of tokens to generate.
   */
  public void setMaxTokens(int maxTokens) {
    this.maxTokens = Math.max(1, maxTokens);
  }
  
  /**
   * Returns the top-p parameter (0.0 to 1.0).
   * Controls nucleus sampling - lower values focus on more likely tokens.
   */
  public double getTopP() {
    return topP;
  }
  
  /**
   * Sets the top-p parameter (0.0 to 1.0).
   */
  public void setTopP(double topP) {
    this.topP = Math.max(0.0, Math.min(1.0, topP));
  }
  
  /**
   * Returns the frequency penalty (-2.0 to 2.0).
   * Positive values decrease likelihood of repeating tokens.
   */
  public int getFrequencyPenalty() {
    return frequencyPenalty;
  }
  
  /**
   * Sets the frequency penalty (-2.0 to 2.0).
   */
  public void setFrequencyPenalty(int frequencyPenalty) {
    this.frequencyPenalty = Math.max(-2, Math.min(2, frequencyPenalty));
  }
  
  /**
   * Returns the presence penalty (-2.0 to 2.0).
   * Positive values increase likelihood of talking about new topics.
   */
  public int getPresencePenalty() {
    return presencePenalty;
  }
  
  /**
   * Sets the presence penalty (-2.0 to 2.0).
   */
  public void setPresencePenalty(int presencePenalty) {
    this.presencePenalty = Math.max(-2, Math.min(2, presencePenalty));
  }
  
  @Override
  public String toString() {
    return "AIModelParameters{" +
           "temperature=" + temperature +
           ", maxTokens=" + maxTokens +
           ", topP=" + topP +
           ", frequencyPenalty=" + frequencyPenalty +
           ", presencePenalty=" + presencePenalty +
           '}';
  }
}

</file>
<file path="AIPlugin.java">
/*
 * AIPlugin.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.plugin.Plugin;
import com.eteks.sweethome3d.plugin.PluginAction;

/**
 * Main plugin class for AI integration in Sweet Home 3D.
 * This plugin provides AI-powered floor plan analysis capabilities.
 * 
 * <AUTHOR> Kpassegna
 */
public class AIPlugin extends Plugin {
  
  /**
   * Returns the actions provided by this plugin.
   */
  @Override
  public PluginAction[] getActions() {
    return new PluginAction[] {
      new AIAction(getHomeController())
    };
  }
  
  /**
   * Called when the plugin is destroyed.
   * Cleans up any resources used by the plugin.
   */
  @Override
  public void destroy() {
    super.destroy();
    // Any cleanup code would go here
  }
}

</file>
<file path="AIPluginValidator.java">
/*
 * AIPluginValidator.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.Wall;
import com.eteks.sweethome3d.model.Room;
import com.eteks.sweethome3d.model.HomePieceOfFurniture;
import java.util.ArrayList;
import java.util.List;

/**
 * Validator for testing AI plugin functionality.
 * Provides methods to validate plugin components and integration.
 * 
 * <AUTHOR> Kpassegna
 */
public class AIPluginValidator {
  
  /**
   * Validates the basic plugin configuration system.
   * 
   * @return List of validation results
   */
  public List<String> validateConfigurationSystem() {
    List<String> results = new ArrayList<>();
    
    try {
      // Test configuration creation
      AIProviderConfig config = AIProviderConfig.builder()
          .providerName("Test Provider")
          .baseUrl("https://api.example.com/v1")
          .apiKey("test-key")
          .model("test-model")
          .build();
      
      results.add("✓ Configuration creation successful");
      
      // Test configuration validation
      AIConfigValidator validator = new AIConfigValidator();
      ValidationResult validationResult = validator.validate(config);
      
      if (validationResult.isValid()) {
        results.add("✓ Configuration validation successful");
      } else {
        results.add("✗ Configuration validation failed: " + validationResult.getFirstError());
      }
      
      // Test configuration manager
      AIConfigurationManager configManager = new AIConfigurationManager();
      configManager.saveConfiguration(config);
      AIProviderConfig loadedConfig = configManager.loadConfiguration();
      
      if (loadedConfig != null && loadedConfig.getProviderName().equals("Test Provider")) {
        results.add("✓ Configuration save/load successful");
      } else {
        results.add("✗ Configuration save/load failed");
      }
      
    } catch (Exception e) {
      results.add("✗ Configuration system error: " + e.getMessage());
    }
    
    return results;
  }
  
  /**
   * Validates the data extraction system.
   * 
   * @return List of validation results
   */
  public List<String> validateDataExtraction() {
    List<String> results = new ArrayList<>();
    
    try {
      // Create a test home
      Home testHome = createTestHome();
      
      // Test data extraction
      FloorPlanDataExtractor extractor = new FloorPlanDataExtractor();
      String jsonData = extractor.extractToJson(testHome);
      
      if (jsonData != null && !jsonData.isEmpty()) {
        results.add("✓ Data extraction successful");
        
        // Validate JSON structure
        if (jsonData.contains("\"home\"") && jsonData.contains("\"walls\"") && 
            jsonData.contains("\"rooms\"") && jsonData.contains("\"furniture\"")) {
          results.add("✓ JSON structure validation successful");
        } else {
          results.add("✗ JSON structure validation failed");
        }
        
        // Test privacy sanitization
        PrivacyManager privacyManager = new PrivacyManager();
        String sanitizedData = privacyManager.sanitizeFloorPlanData(jsonData, false);
        
        if (!sanitizedData.equals(jsonData)) {
          results.add("✓ Privacy sanitization working");
        } else {
          results.add("? Privacy sanitization may not be working (no changes detected)");
        }
        
      } else {
        results.add("✗ Data extraction failed");
      }
      
    } catch (Exception e) {
      results.add("✗ Data extraction error: " + e.getMessage());
    }
    
    return results;
  }
  
  /**
   * Validates the AI client factory.
   * 
   * @return List of validation results
   */
  public List<String> validateAIClientFactory() {
    List<String> results = new ArrayList<>();
    
    try {
      // Test valid configuration
      AIProviderConfig validConfig = AIProviderConfig.builder()
          .providerName("Test Provider")
          .baseUrl("http://localhost:11434/v1")
          .apiKey("test-key")
          .model("test-model")
          .build();
      
      AIClient client = AIClientFactory.createClient(validConfig);
      if (client != null) {
        results.add("✓ AI client creation successful");
        client.close();
      } else {
        results.add("✗ AI client creation failed");
      }
      
      // Test invalid configuration
      try {
        AIProviderConfig invalidConfig = AIProviderConfig.builder()
            .providerName("Invalid")
            .baseUrl("")
            .model("")
            .build();
        
        AIClientFactory.createClient(invalidConfig);
        results.add("✗ Invalid configuration should have been rejected");
      } catch (IllegalArgumentException e) {
        results.add("✓ Invalid configuration properly rejected");
      }
      
    } catch (Exception e) {
      results.add("✗ AI client factory error: " + e.getMessage());
    }
    
    return results;
  }
  
  /**
   * Validates the provider presets.
   * 
   * @return List of validation results
   */
  public List<String> validateProviderPresets() {
    List<String> results = new ArrayList<>();
    
    try {
      int presetCount = 0;
      for (AIProviderPreset preset : AIProviderPreset.values()) {
        if (preset != AIProviderPreset.CUSTOM) {
          presetCount++;
          
          // Validate preset has required fields
          if (preset.getDisplayName() == null || preset.getDisplayName().isEmpty()) {
            results.add("✗ Preset " + preset + " missing display name");
          }
          
          if (preset.getBaseUrl() == null || preset.getBaseUrl().isEmpty()) {
            results.add("✗ Preset " + preset + " missing base URL");
          }
        }
      }
      
      results.add("✓ Found " + presetCount + " provider presets");
      
      // Test preset configuration creation
      AIProviderConfig config = AIProviderPreset.OLLAMA.createConfig("test-key");
      if (config.getProviderName().equals("Ollama (Local)")) {
        results.add("✓ Preset configuration creation successful");
      } else {
        results.add("✗ Preset configuration creation failed");
      }
      
    } catch (Exception e) {
      results.add("✗ Provider preset validation error: " + e.getMessage());
    }
    
    return results;
  }
  
  /**
   * Creates a test home for validation purposes.
   * 
   * @return A test home with basic elements
   */
  private Home createTestHome() {
    Home home = new Home();
    home.setName("Test Home");
    
    // Add a test wall
    Wall wall = new Wall(0, 0, 100, 0, 10);
    wall.setHeight(250f);
    home.addWall(wall);
    
    // Add a test room
    Room room = new Room(new float[][] {{0, 0}, {100, 0}, {100, 100}, {0, 100}});
    room.setName("Test Room");
    home.addRoom(room);
    
    return home;
  }
  
  /**
   * Runs all validation tests and returns a comprehensive report.
   * 
   * @return Complete validation report
   */
  public String runAllValidations() {
    StringBuilder report = new StringBuilder();
    report.append("AI Plugin Validation Report\n");
    report.append("===========================\n\n");
    
    report.append("Configuration System:\n");
    for (String result : validateConfigurationSystem()) {
      report.append("  ").append(result).append("\n");
    }
    report.append("\n");
    
    report.append("Data Extraction:\n");
    for (String result : validateDataExtraction()) {
      report.append("  ").append(result).append("\n");
    }
    report.append("\n");
    
    report.append("AI Client Factory:\n");
    for (String result : validateAIClientFactory()) {
      report.append("  ").append(result).append("\n");
    }
    report.append("\n");
    
    report.append("Provider Presets:\n");
    for (String result : validateProviderPresets()) {
      report.append("  ").append(result).append("\n");
    }
    report.append("\n");
    
    report.append("Validation completed.\n");
    return report.toString();
  }
}

</file>
<file path="AIProviderConfig.java">
/*
 * AIProviderConfig.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Configuration for AI providers supporting OpenAI-compatible endpoints.
 * This class stores all necessary information to connect to various AI services
 * including commercial providers, API aggregators, and local LLM tools.
 * 
 * <AUTHOR> Kpassegna
 */
public class AIProviderConfig implements Serializable {
  private static final long serialVersionUID = 1L;
  
  private String providerName;
  private String baseUrl;
  private String apiKey;
  private String model;
  private Map<String, String> customHeaders;
  private AIModelParameters modelParams;
  
  /**
   * Creates a new AI provider configuration.
   */
  public AIProviderConfig() {
    this.customHeaders = new HashMap<>();
    this.modelParams = new AIModelParameters();
  }
  
  /**
   * Creates a new AI provider configuration with the specified parameters.
   */
  public AIProviderConfig(String providerName, String baseUrl, String apiKey, String model) {
    this();
    this.providerName = providerName;
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
    this.model = model;
  }
  
  /**
   * Returns the display name of the provider.
   */
  public String getProviderName() {
    return providerName;
  }
  
  /**
   * Sets the display name of the provider.
   */
  public void setProviderName(String providerName) {
    this.providerName = providerName;
  }
  
  /**
   * Returns the base URL of the API endpoint.
   */
  public String getBaseUrl() {
    return baseUrl;
  }
  
  /**
   * Sets the base URL of the API endpoint.
   */
  public void setBaseUrl(String baseUrl) {
    this.baseUrl = baseUrl;
  }
  
  /**
   * Returns the API key for authentication.
   */
  public String getApiKey() {
    return apiKey;
  }
  
  /**
   * Sets the API key for authentication.
   */
  public void setApiKey(String apiKey) {
    this.apiKey = apiKey;
  }
  
  /**
   * Returns the model identifier.
   */
  public String getModel() {
    return model;
  }
  
  /**
   * Sets the model identifier.
   */
  public void setModel(String model) {
    this.model = model;
  }
  
  /**
   * Returns custom headers for self-hosted providers.
   */
  public Map<String, String> getCustomHeaders() {
    return customHeaders;
  }
  
  /**
   * Sets custom headers for self-hosted providers.
   */
  public void setCustomHeaders(Map<String, String> customHeaders) {
    this.customHeaders = customHeaders != null ? customHeaders : new HashMap<>();
  }
  
  /**
   * Returns the model parameters.
   */
  public AIModelParameters getModelParams() {
    return modelParams;
  }
  
  /**
   * Sets the model parameters.
   */
  public void setModelParams(AIModelParameters modelParams) {
    this.modelParams = modelParams != null ? modelParams : new AIModelParameters();
  }
  
  /**
   * Creates a new builder for AIProviderConfig.
   */
  public static Builder builder() {
    return new Builder();
  }
  
  /**
   * Builder pattern implementation for AIProviderConfig.
   */
  public static class Builder {
    private AIProviderConfig config;
    
    public Builder() {
      this.config = new AIProviderConfig();
    }
    
    public Builder providerName(String providerName) {
      config.setProviderName(providerName);
      return this;
    }
    
    public Builder baseUrl(String baseUrl) {
      config.setBaseUrl(baseUrl);
      return this;
    }
    
    public Builder apiKey(String apiKey) {
      config.setApiKey(apiKey);
      return this;
    }
    
    public Builder model(String model) {
      config.setModel(model);
      return this;
    }
    
    public Builder customHeaders(Map<String, String> customHeaders) {
      config.setCustomHeaders(customHeaders);
      return this;
    }
    
    public Builder modelParams(AIModelParameters modelParams) {
      config.setModelParams(modelParams);
      return this;
    }
    
    public AIProviderConfig build() {
      return config;
    }
  }
  
  @Override
  public String toString() {
    return "AIProviderConfig{" +
           "providerName='" + providerName + '\'' +
           ", baseUrl='" + baseUrl + '\'' +
           ", model='" + model + '\'' +
           ", hasApiKey=" + (apiKey != null && !apiKey.isEmpty()) +
           '}';
  }
}

</file>
<file path="AIProviderPreset.java">
/*
 * AIProviderPreset.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Pre-configured settings for popular AI providers as of September 2025.
 * Includes major commercial providers, API aggregators, and local LLM tools.
 * 
 * <AUTHOR> Kpassegna
 */
public enum AIProviderPreset {
  // Major Commercial Providers (September 2025)
  OPENAI("OpenAI", "https://api.openai.com/v1",
         Arrays.asList("gpt-5", "gpt-4.1", "o3", "o4-mini", "gpt-4o")),
  
  ANTHROPIC("Anthropic", "https://api.anthropic.com/v1",
            Arrays.asList("claude-opus-4.1", "claude-sonnet-4", "claude-3.5-sonnet")),
  
  GOOGLE("Google AI", "https://generativelanguage.googleapis.com/v1",
         Arrays.asList("gemini-2.5-pro", "gemini-2.0-flash", "gemini-pro")),
  
  XAI("xAI", "https://api.x.ai/v1",
      Arrays.asList("grok-4", "grok-3", "grok-2")),
  
  // API Aggregators & Platforms
  TOGETHER_AI("Together AI", "https://api.together.xyz/v1",
              Arrays.asList("meta-llama/Llama-4-Scout", "mistralai/Mistral-7B-Instruct", "deepseek-ai/deepseek-r1")),
  
  FIREWORKS("Fireworks AI", "https://api.fireworks.ai/inference/v1",
            Arrays.asList("accounts/fireworks/models/llama-v3p1-405b-instruct", "accounts/fireworks/models/mixtral-8x7b-instruct")),
  
  OPENROUTER("OpenRouter", "https://openrouter.ai/api/v1",
             Arrays.asList("anthropic/claude-3.5-sonnet", "openai/gpt-4", "meta-llama/llama-3.1-405b")),
  
  GROQ("Groq", "https://api.groq.com/openai/v1",
       Arrays.asList("llama-3.1-405b-reasoning", "llama-3.1-70b-versatile", "mixtral-8x7b-32768")),
  
  DEEPINFRA("DeepInfra", "https://api.deepinfra.com/v1/openai",
            Arrays.asList("meta-llama/Meta-Llama-3.1-405B-Instruct", "microsoft/WizardLM-2-8x22B")),
  
  // Local & Self-Hosted Solutions
  OLLAMA("Ollama (Local)", "http://localhost:11434/v1",
         Arrays.asList("llama3.3", "qwen2.5", "deepseek-r1", "phi3", "mistral")),
  
  LM_STUDIO("LM Studio (Local)", "http://localhost:1234/v1",
            Arrays.asList("llama-3.1-8b-instruct", "phi-3-mini-4k-instruct", "mistral-7b-instruct")),
  
  ANYTHINGLLM("AnythingLLM (Local)", "http://localhost:3001/api/v1",
              Arrays.asList("llama3", "mistral", "codellama")),
  
  JAN("Jan (Local)", "http://localhost:1337/v1",
      Arrays.asList("llama3-8b", "phi3-mini", "gemma-2b")),
  
  CUSTOM("Custom Provider", "", Collections.emptyList());
  
  private final String displayName;
  private final String baseUrl;
  private final List<String> defaultModels;
  
  AIProviderPreset(String displayName, String baseUrl, List<String> defaultModels) {
    this.displayName = displayName;
    this.baseUrl = baseUrl;
    this.defaultModels = defaultModels;
  }
  
  /**
   * Returns the display name of the provider.
   */
  public String getDisplayName() {
    return displayName;
  }
  
  /**
   * Returns the base URL for the provider's API.
   */
  public String getBaseUrl() {
    return baseUrl;
  }
  
  /**
   * Returns the list of default models for this provider.
   */
  public List<String> getDefaultModels() {
    return Collections.unmodifiableList(defaultModels);
  }
  
  /**
   * Creates an AIProviderConfig from this preset.
   */
  public AIProviderConfig createConfig() {
    return AIProviderConfig.builder()
        .providerName(displayName)
        .baseUrl(baseUrl)
        .model(defaultModels.isEmpty() ? "" : defaultModels.get(0))
        .build();
  }
  
  /**
   * Creates an AIProviderConfig from this preset with the specified API key.
   */
  public AIProviderConfig createConfig(String apiKey) {
    return AIProviderConfig.builder()
        .providerName(displayName)
        .baseUrl(baseUrl)
        .apiKey(apiKey)
        .model(defaultModels.isEmpty() ? "" : defaultModels.get(0))
        .build();
  }
  
  /**
   * Returns whether this provider requires an API key.
   */
  public boolean requiresApiKey() {
    // Local providers typically don't require API keys
    return this != OLLAMA && this != LM_STUDIO && this != ANYTHINGLLM && this != JAN && this != CUSTOM;
  }
  
  @Override
  public String toString() {
    return displayName;
  }
}

</file>
<file path="AIResourceBundle.java">
/*
 * AIResourceBundle.java 18 Sep 2025
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 */
package com.eteks.sweethome3d.plugin.ai;

import java.text.MessageFormat;
import java.util.Locale;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

/**
 * Utility class for accessing internationalized strings for the AI plugin.
 * This class provides a centralized way to access localized strings from
 * the ApplicationPlugin resource bundles.
 *
 * <AUTHOR> Kpassegna
 * @deprecated Use UserPreferences.getLocalizedString() directly for better integration with SweetHome 3D's internationalization system
 */
@Deprecated
public class AIResourceBundle {
  private static final String BUNDLE_NAME = "com.eteks.sweethome3d.plugin.ai.ApplicationPlugin";
  private static ResourceBundle resourceBundle;
  
  static {
    try {
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.getDefault());
    } catch (MissingResourceException ex) {
      // Fallback to default locale if current locale is not available
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.ENGLISH);
    }
  }
  
  /**
   * Returns the localized string for the given key.
   * 
   * @param key the resource key
   * @return the localized string, or the key itself if not found
   */
  public static String getString(String key) {
    try {
      return resourceBundle.getString(key);
    } catch (MissingResourceException ex) {
      // Return the key itself if the resource is not found
      return key;
    }
  }
  
  /**
   * Returns the localized string for the given key with formatted arguments.
   * 
   * @param key the resource key
   * @param arguments the arguments to format into the string
   * @return the formatted localized string
   */
  public static String getString(String key, Object... arguments) {
    try {
      String pattern = resourceBundle.getString(key);
      return MessageFormat.format(pattern, arguments);
    } catch (MissingResourceException ex) {
      // Return the key itself if the resource is not found
      return key;
    }
  }
  
  /**
   * Returns the localized string for the given key, or the default value if not found.
   * 
   * @param key the resource key
   * @param defaultValue the default value to return if key is not found
   * @return the localized string or default value
   */
  public static String getString(String key, String defaultValue) {
    try {
      return resourceBundle.getString(key);
    } catch (MissingResourceException ex) {
      return defaultValue;
    }
  }
  
  /**
   * Reloads the resource bundle for the current locale.
   * This can be useful if the locale has changed during runtime.
   */
  public static void reload() {
    try {
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.getDefault());
    } catch (MissingResourceException ex) {
      // Fallback to default locale if current locale is not available
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.ENGLISH);
    }
  }
  
  /**
   * Reloads the resource bundle for the specified locale.
   * 
   * @param locale the locale to load
   */
  public static void reload(Locale locale) {
    try {
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, locale);
    } catch (MissingResourceException ex) {
      // Fallback to default locale if specified locale is not available
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.ENGLISH);
    }
  }
  
  /**
   * Returns the current locale of the resource bundle.
   * 
   * @return the current locale
   */
  public static Locale getLocale() {
    return resourceBundle.getLocale();
  }
  
  // Convenience methods for commonly used strings
  
  /**
   * Returns the localized action name.
   */
  public static String getActionName() {
    return getString("AIAction.Name", "AI Analysis");
  }
  
  /**
   * Returns the localized action description.
   */
  public static String getActionDescription() {
    return getString("AIAction.ShortDescription", "Analyze floor plan with AI");
  }
  
  /**
   * Returns the localized chat dialog title.
   */
  public static String getChatDialogTitle() {
    return getString("AIChatDialog.title", "AI Floor Plan Analysis");
  }
  
  /**
   * Returns the localized settings dialog title.
   */
  public static String getSettingsDialogTitle() {
    return getString("AISettingsDialog.title", "AI Settings");
  }
  
  /**
   * Returns the localized button text for the given button key.
   * 
   * @param buttonKey the button key (e.g., "send", "save", "cancel")
   * @return the localized button text
   */
  public static String getButtonText(String buttonKey) {
    return getString("button." + buttonKey, buttonKey);
  }
  
  /**
   * Returns the localized label text for the given label key.
   * 
   * @param labelKey the label key (e.g., "provider", "apiKey", "model")
   * @return the localized label text
   */
  public static String getLabelText(String labelKey) {
    return getString("label." + labelKey, labelKey + ":");
  }
  
  /**
   * Returns the localized message text for the given message key.
   * 
   * @param messageKey the message key (e.g., "analyzing", "connectionSuccessful")
   * @return the localized message text
   */
  public static String getMessageText(String messageKey) {
    return getString("message." + messageKey, messageKey);
  }
  
  /**
   * Returns the localized error message for the given error key.
   * 
   * @param errorKey the error key (e.g., "analysisError", "connectionError")
   * @param arguments optional arguments to format into the error message
   * @return the localized error message
   */
  public static String getErrorMessage(String errorKey, Object... arguments) {
    return getString("error." + errorKey, arguments);
  }
  
  /**
   * Returns the localized provider name for the given provider key.
   * 
   * @param providerKey the provider key (e.g., "openai", "anthropic", "custom")
   * @return the localized provider name
   */
  public static String getProviderName(String providerKey) {
    return getString("provider." + providerKey, providerKey);
  }
  
  /**
   * Returns the localized analysis prompt.
   */
  public static String getAnalysisPrompt() {
    return getString("analysis.prompt", 
        "Please analyze this floor plan and provide comprehensive insights including:\n" +
        "1. Layout efficiency and space utilization\n" +
        "2. Traffic flow and circulation patterns\n" +
        "3. Natural lighting and ventilation opportunities\n" +
        "4. Accessibility considerations\n" +
        "5. Functional relationships between spaces\n" +
        "6. Suggestions for improvement\n" +
        "7. Compliance with common building standards\n" +
        "8. Energy efficiency considerations\n\n" +
        "Please provide specific, actionable recommendations that would enhance " +
        "the functionality, comfort, and aesthetic appeal of this space.");
  }
}

</file>
<file path="AISettingsDialog.java">
/*
 * AISettingsDialog.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.UserPreferences;
import com.eteks.sweethome3d.viewcontroller.HomeController;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.lang.ref.WeakReference;
import java.util.List;

/**
 * Dialog for configuring AI provider settings.
 * Allows users to select providers, configure endpoints, and test connections.
 * Uses SweetHome 3D's internationalization system for proper localization.
 *
 * <AUTHOR> Kpassegna
 */
public class AISettingsDialog extends JDialog {
  private final HomeController homeController;
  private final AIIntegration aiIntegration;
  private final UserPreferences preferences;
  private boolean configurationSaved = false;
  
  // UI Components
  private JComboBox<AIProviderPreset> providerComboBox;
  private JTextField baseUrlField;
  private JPasswordField apiKeyField;
  private JComboBox<String> modelComboBox;
  private JSlider temperatureSlider;
  private JSpinner maxTokensSpinner;
  private JButton testConnectionButton;
  private JButton saveButton;
  private JButton cancelButton;
  private JProgressBar progressBar;
  private JLabel statusLabel;
  
  /**
   * Creates a new AI settings dialog.
   */
  public AISettingsDialog(HomeController homeController, AIIntegration aiIntegration) {
    this.homeController = homeController;
    this.aiIntegration = aiIntegration;
    this.preferences = homeController.getUserPreferences();

    // Set localized title
    String title = getLocalizedString("AISettingsDialog.title", "AI Settings");
    setTitle(title);
    setModal(true);

    initializeComponents();
    layoutComponents();
    loadCurrentConfiguration();
    setupEventHandlers();

    setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    pack();
    setLocationRelativeTo(SwingUtilities.getWindowAncestor(homeController.getView()));

    // Listen for language changes to update UI
    this.preferences.addPropertyChangeListener(UserPreferences.Property.LANGUAGE,
        new LanguageChangeListener(this));
  }

  /**
   * Preferences property listener bound to this dialog with a weak reference to avoid
   * strong link between preferences and this dialog.
   */
  private static class LanguageChangeListener implements PropertyChangeListener {
    private final WeakReference<AISettingsDialog> dialog;

    public LanguageChangeListener(AISettingsDialog dialog) {
      this.dialog = new WeakReference<AISettingsDialog>(dialog);
    }

    public void propertyChange(PropertyChangeEvent ev) {
      // If dialog was garbage collected, remove this listener from preferences
      AISettingsDialog dialog = this.dialog.get();
      if (dialog == null) {
        ((UserPreferences)ev.getSource()).removePropertyChangeListener(
            UserPreferences.Property.LANGUAGE, this);
      } else {
        dialog.updateLocalizedStrings();
      }
    }
  }

  /**
   * Returns a localized string for the given key.
   */
  private String getLocalizedString(String key, String defaultValue) {
    try {
      return preferences.getLocalizedString(AISettingsDialog.class, key);
    } catch (IllegalArgumentException ex) {
      return defaultValue;
    }
  }

  /**
   * Updates all localized strings in the UI.
   */
  private void updateLocalizedStrings() {
    setTitle(getLocalizedString("AISettingsDialog.title", "AI Settings"));
    testConnectionButton.setText(getLocalizedString("button.testConnection", "Test Connection"));
    saveButton.setText(getLocalizedString("button.save", "Save"));
    cancelButton.setText(getLocalizedString("button.cancel", "Cancel"));
  }
  
  /**
   * Initializes the UI components.
   */
  private void initializeComponents() {
    // Provider selection
    providerComboBox = new JComboBox<>(AIProviderPreset.values());
    providerComboBox.setRenderer(new DefaultListCellRenderer() {
      @Override
      public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                                                  boolean isSelected, boolean cellHasFocus) {
        super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
        if (value instanceof AIProviderPreset) {
          setText(((AIProviderPreset) value).getDisplayName());
        }
        return this;
      }
    });
    
    // Connection fields
    baseUrlField = new JTextField(30);
    apiKeyField = new JPasswordField(30);
    modelComboBox = new JComboBox<>();
    modelComboBox.setEditable(true);
    
    // Model parameters
    temperatureSlider = new JSlider(0, 200, 70); // 0.0 to 2.0, default 0.7
    temperatureSlider.setMajorTickSpacing(50);
    temperatureSlider.setMinorTickSpacing(10);
    temperatureSlider.setPaintTicks(true);
    temperatureSlider.setPaintLabels(true);
    
    // Create custom labels for temperature slider
    java.util.Hashtable<Integer, JLabel> labelTable = new java.util.Hashtable<>();
    labelTable.put(0, new JLabel("0.0"));
    labelTable.put(50, new JLabel("0.5"));
    labelTable.put(100, new JLabel("1.0"));
    labelTable.put(150, new JLabel("1.5"));
    labelTable.put(200, new JLabel("2.0"));
    temperatureSlider.setLabelTable(labelTable);
    
    maxTokensSpinner = new JSpinner(new SpinnerNumberModel(2048, 1, 8192, 1));
    
    // Action buttons
    testConnectionButton = new JButton(getLocalizedString("button.testConnection", "Test Connection"));
    saveButton = new JButton(getLocalizedString("button.save", "Save"));
    cancelButton = new JButton(getLocalizedString("button.cancel", "Cancel"));

    // Status components
    progressBar = new JProgressBar();
    progressBar.setIndeterminate(true);
    progressBar.setVisible(false);
    statusLabel = new JLabel(" ");
  }
  
  /**
   * Layouts the components in the dialog.
   */
  private void layoutComponents() {
    setLayout(new BorderLayout());
    
    // Main panel
    JPanel mainPanel = new JPanel(new GridBagLayout());
    GridBagConstraints gbc = new GridBagConstraints();
    gbc.insets = new Insets(5, 5, 5, 5);
    
    // Provider selection
    gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
    mainPanel.add(new JLabel(getLocalizedString("label.provider", "Provider:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(providerComboBox, gbc);

    // Base URL
    gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel(getLocalizedString("label.baseUrl", "Base URL:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(baseUrlField, gbc);

    // API Key
    gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel(getLocalizedString("label.apiKey", "API Key:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(apiKeyField, gbc);

    // Model
    gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel(getLocalizedString("label.model", "Model:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(modelComboBox, gbc);

    // Temperature
    gbc.gridx = 0; gbc.gridy = 4; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel(getLocalizedString("label.temperature", "Temperature:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(temperatureSlider, gbc);
    
    // Max Tokens
    gbc.gridx = 0; gbc.gridy = 5; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel("Max Tokens:"), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(maxTokensSpinner, gbc);
    
    // Test connection button
    gbc.gridx = 0; gbc.gridy = 6; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.NONE;
    gbc.anchor = GridBagConstraints.CENTER; gbc.weightx = 0;
    mainPanel.add(testConnectionButton, gbc);
    
    // Progress bar
    gbc.gridx = 0; gbc.gridy = 7; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
    gbc.weightx = 1.0;
    mainPanel.add(progressBar, gbc);
    
    // Status label
    gbc.gridx = 0; gbc.gridy = 8; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
    mainPanel.add(statusLabel, gbc);
    
    add(mainPanel, BorderLayout.CENTER);
    
    // Button panel
    JPanel buttonPanel = new JPanel(new FlowLayout());
    buttonPanel.add(saveButton);
    buttonPanel.add(cancelButton);
    add(buttonPanel, BorderLayout.SOUTH);
  }
  
  /**
   * Loads the current configuration into the dialog.
   */
  private void loadCurrentConfiguration() {
    AIProviderConfig config = aiIntegration.getCurrentConfiguration();
    if (config != null) {
      // Find matching preset
      AIProviderPreset matchingPreset = AIProviderPreset.CUSTOM;
      for (AIProviderPreset preset : AIProviderPreset.values()) {
        if (preset.getBaseUrl().equals(config.getBaseUrl())) {
          matchingPreset = preset;
          break;
        }
      }
      
      providerComboBox.setSelectedItem(matchingPreset);
      baseUrlField.setText(config.getBaseUrl());
      apiKeyField.setText(config.getApiKey());
      
      // Update model list and select current model
      updateModelList(matchingPreset);
      if (config.getModel() != null) {
        modelComboBox.setSelectedItem(config.getModel());
      }
      
      // Load model parameters
      AIModelParameters params = config.getModelParams();
      if (params != null) {
        temperatureSlider.setValue((int) (params.getTemperature() * 100));
        maxTokensSpinner.setValue(params.getMaxTokens());
      }
    }
  }
  
  /**
   * Sets up event handlers for the components.
   */
  private void setupEventHandlers() {
    providerComboBox.addActionListener(this::onProviderChanged);
    testConnectionButton.addActionListener(this::onTestConnection);
    saveButton.addActionListener(this::onSave);
    cancelButton.addActionListener(this::onCancel);
  }
  
  /**
   * Handles provider selection changes.
   */
  private void onProviderChanged(ActionEvent e) {
    AIProviderPreset preset = (AIProviderPreset) providerComboBox.getSelectedItem();
    if (preset != null && preset != AIProviderPreset.CUSTOM) {
      baseUrlField.setText(preset.getBaseUrl());
      updateModelList(preset);
      
      // Clear API key field for new provider
      if (!preset.getBaseUrl().equals(baseUrlField.getText())) {
        apiKeyField.setText("");
      }
    } else {
      baseUrlField.setText("");
      modelComboBox.removeAllItems();
    }
  }
  
  /**
   * Updates the model list based on the selected preset.
   */
  private void updateModelList(AIProviderPreset preset) {
    modelComboBox.removeAllItems();
    for (String model : preset.getDefaultModels()) {
      modelComboBox.addItem(model);
    }
  }
  
  /**
   * Handles connection testing.
   */
  private void onTestConnection(ActionEvent e) {
    // Create temporary configuration from UI
    AIProviderConfig tempConfig = createConfigFromUI();

    // Validate basic configuration first
    AIConfigValidator validator = new AIConfigValidator();
    ValidationResult result = validator.validate(tempConfig);

    if (!result.isValid()) {
      statusLabel.setText("Configuration invalid: " + result.getFirstError());
      statusLabel.setForeground(Color.RED);
      return;
    }

    // Start connection test
    testConnectionButton.setEnabled(false);
    progressBar.setVisible(true);
    statusLabel.setText("Testing connection...");
    statusLabel.setForeground(Color.BLACK);

    // Run test in background thread
    SwingWorker<Boolean, Void> worker = new SwingWorker<Boolean, Void>() {
      @Override
      protected Boolean doInBackground() throws Exception {
        try {
          AIClient testClient = AIClientFactory.createClient(tempConfig);
          boolean success = testClient.testConnection();
          testClient.close();
          return success;
        } catch (Exception e) {
          throw e;
        }
      }

      @Override
      protected void done() {
        testConnectionButton.setEnabled(true);
        progressBar.setVisible(false);

        try {
          boolean success = get();
          if (success) {
            statusLabel.setText("Connection successful!");
            statusLabel.setForeground(new Color(0, 128, 0)); // Dark green
          } else {
            statusLabel.setText("Connection failed");
            statusLabel.setForeground(Color.RED);
          }
        } catch (Exception e) {
          AIErrorHandler errorHandler = new AIErrorHandler();
          String errorMessage = errorHandler.getConnectionTestErrorMessage(e);
          statusLabel.setText(errorMessage);
          statusLabel.setForeground(Color.RED);
        }
      }
    };

    worker.execute();
  }
  
  /**
   * Handles save action.
   */
  private void onSave(ActionEvent e) {
    // Create configuration from UI
    AIProviderConfig config = createConfigFromUI();
    
    // Validate configuration
    AIConfigValidator validator = new AIConfigValidator();
    ValidationResult result = validator.validate(config);
    
    if (!result.isValid()) {
      String errors = String.join("\n", result.getErrors());
      JOptionPane.showMessageDialog(this, "Configuration errors:\n" + errors, 
                                  "Validation Error", JOptionPane.ERROR_MESSAGE);
      return;
    }
    
    // Save configuration
    aiIntegration.saveConfiguration(config);
    configurationSaved = true;
    dispose();
  }
  
  /**
   * Handles cancel action.
   */
  private void onCancel(ActionEvent e) {
    dispose();
  }
  
  /**
   * Creates a configuration object from the UI values.
   */
  private AIProviderConfig createConfigFromUI() {
    AIProviderPreset preset = (AIProviderPreset) providerComboBox.getSelectedItem();
    String providerName = preset != null ? preset.getDisplayName() : "Custom";
    
    AIModelParameters params = new AIModelParameters();
    params.setTemperature(temperatureSlider.getValue() / 100.0);
    params.setMaxTokens((Integer) maxTokensSpinner.getValue());
    
    return AIProviderConfig.builder()
        .providerName(providerName)
        .baseUrl(baseUrlField.getText().trim())
        .apiKey(new String(apiKeyField.getPassword()))
        .model(modelComboBox.getSelectedItem() != null ? modelComboBox.getSelectedItem().toString() : "")
        .modelParams(params)
        .build();
  }
  
  /**
   * Returns whether the configuration was saved.
   */
  public boolean isConfigurationSaved() {
    return configurationSaved;
  }
}

</file>
<file path="ApplicationPlugin.properties">
# ApplicationPlugin.properties
# Sweet Home 3D AI Plugin Configuration
# Copyright (c) 2025 Samuel Kpassegna

# Plugin identification
id=ai-floor-plan-analysis
name=AI Floor Plan Analysis
description=Analyze floor plans using artificial intelligence to provide insights and improvement suggestions
version=1.0.0
license=GPL v2+
provider=Samuel Kpassegna

# Plugin class
class=com.eteks.sweethome3d.plugin.ai.AIPlugin

# Java version requirement
javaMinimumVersion=1.8

# Plugin information
author=Samuel Kpassegna
website=https://github.com/skpassegna/sweethome3d-ai-plugin
supportEmail=<EMAIL>

# Features
features=AI Analysis, Floor Plan Insights, Multiple AI Providers, Privacy Controls

# Requirements
requirements=Internet connection for cloud AI providers (optional for local providers)

# Compatibility
sweetHome3DMinimumVersion=7.0

# UI Strings for internationalization
# Action properties
AIAction.Name=AI Analysis
AIAction.ShortDescription=Analyze floor plan with AI
AIAction.Menu=Tools

# Dialog titles
AIChatDialog.title=AI Floor Plan Analysis
AISettingsDialog.title=AI Settings

# Button labels
button.send=Send
button.newAnalysis=New Analysis
button.settings=Settings
button.testConnection=Test Connection
button.save=Save
button.cancel=Cancel

# Labels
label.provider=Provider:
label.baseUrl=Base URL:
label.apiKey=API Key:
label.model=Model:
label.temperature=Temperature:
label.maxTokens=Max Tokens:
label.status=Status:

# Messages
message.analyzing=Analyzing floor plan...
message.processingQuestion=Processing question...
message.testingConnection=Testing connection...
message.connectionSuccessful=Connection successful!
message.connectionFailed=Connection failed: {0}
message.configurationSaved=Configuration saved successfully
message.validationError=Configuration errors:\n{0}
message.noConfiguration=AI provider not configured. Please configure settings first.

# Analysis prompt
analysis.prompt=Please analyze this floor plan and provide comprehensive insights including:\n1. Layout efficiency and space utilization\n2. Traffic flow and circulation patterns\n3. Natural lighting and ventilation opportunities\n4. Accessibility considerations\n5. Functional relationships between spaces\n6. Suggestions for improvement\n7. Compliance with common building standards\n8. Energy efficiency considerations\n\nPlease provide specific, actionable recommendations that would enhance the functionality, comfort, and aesthetic appeal of this space.

# Error messages
error.analysisError=Analysis error: {0}
error.configurationError=Configuration error: {0}
error.connectionError=Connection error: {0}
error.invalidConfiguration=Invalid configuration
error.missingApiKey=API key is required
error.missingBaseUrl=Base URL is required
error.missingModel=Model selection is required

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Local)
provider.lmstudio=LM Studio (Local)
provider.anythingllm=AnythingLLM (Local)
provider.jan=Jan (Local)
provider.custom=Custom

</file>
<file path="FloorPlanDataExtractor.java">
/*
 * FloorPlanDataExtractor.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.HomePieceOfFurniture;
import com.eteks.sweethome3d.model.Wall;
import com.eteks.sweethome3d.model.Room;
import com.eteks.sweethome3d.model.Level;
import com.eteks.sweethome3d.model.Compass;
import com.eteks.sweethome3d.model.HomeEnvironment;
import com.eteks.sweethome3d.model.HomeTexture;

/**
 * Extracts floor plan data from Sweet Home 3D Home objects and converts it to JSON.
 * This class handles the serialization of all relevant home data for AI analysis.
 * 
 * <AUTHOR> Kpassegna
 */
public class FloorPlanDataExtractor {
  
  /**
   * Extracts floor plan data from a Home object and returns it as JSON.
   * 
   * @param home The home to extract data from
   * @return JSON representation of the floor plan data
   */
  public String extractToJson(Home home) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    
    // Home properties
    json.append("  \"home\": {\n");
    json.append("    \"name\": \"").append(escapeJson(home.getName())).append("\",\n");
    json.append("    \"wallHeight\": ").append(home.getWallHeight()).append(",\n");
    json.append("    \"modified\": ").append(home.isModified()).append("\n");
    json.append("  },\n");
    
    // Environment
    json.append("  \"environment\": ");
    json.append(extractEnvironment(home.getEnvironment()));
    json.append(",\n");
    
    // Compass
    json.append("  \"compass\": ");
    json.append(extractCompass(home.getCompass()));
    json.append(",\n");
    
    // Levels
    json.append("  \"levels\": [\n");
    boolean firstLevel = true;
    for (Level level : home.getLevels()) {
      if (!firstLevel) json.append(",\n");
      json.append("    ").append(extractLevel(level));
      firstLevel = false;
    }
    json.append("\n  ],\n");
    
    // Walls
    json.append("  \"walls\": [\n");
    boolean firstWall = true;
    for (Wall wall : home.getWalls()) {
      if (!firstWall) json.append(",\n");
      json.append("    ").append(extractWall(wall));
      firstWall = false;
    }
    json.append("\n  ],\n");
    
    // Rooms
    json.append("  \"rooms\": [\n");
    boolean firstRoom = true;
    for (Room room : home.getRooms()) {
      if (!firstRoom) json.append(",\n");
      json.append("    ").append(extractRoom(room));
      firstRoom = false;
    }
    json.append("\n  ],\n");
    
    // Furniture
    json.append("  \"furniture\": [\n");
    boolean firstFurniture = true;
    for (HomePieceOfFurniture piece : home.getFurniture()) {
      if (!firstFurniture) json.append(",\n");
      json.append("    ").append(extractFurniture(piece));
      firstFurniture = false;
    }
    json.append("\n  ]\n");
    
    json.append("}");
    return json.toString();
  }
  
  /**
   * Extracts environment data.
   */
  private String extractEnvironment(HomeEnvironment env) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("    \"groundColor\": \"#").append(Integer.toHexString(env.getGroundColor())).append("\",\n");
    json.append("    \"skyColor\": \"#").append(Integer.toHexString(env.getSkyColor())).append("\",\n");
    json.append("    \"lightColor\": \"#").append(Integer.toHexString(env.getLightColor())).append("\",\n");
    json.append("    \"wallsAlpha\": ").append(env.getWallsAlpha()).append(",\n");
    json.append("    \"allLevelsVisible\": ").append(env.isAllLevelsVisible()).append("\n");
    json.append("  }");
    return json.toString();
  }
  
  /**
   * Extracts compass data.
   */
  private String extractCompass(Compass compass) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("    \"x\": ").append(compass.getX()).append(",\n");
    json.append("    \"y\": ").append(compass.getY()).append(",\n");
    json.append("    \"diameter\": ").append(compass.getDiameter()).append(",\n");
    json.append("    \"northDirection\": ").append(compass.getNorthDirection()).append(",\n");
    json.append("    \"latitude\": ").append(compass.getLatitude()).append(",\n");
    json.append("    \"longitude\": ").append(compass.getLongitude()).append(",\n");
    json.append("    \"timeZone\": \"").append(escapeJson(compass.getTimeZone())).append("\",\n");
    json.append("    \"visible\": ").append(compass.isVisible()).append("\n");
    json.append("  }");
    return json.toString();
  }
  
  /**
   * Extracts level data.
   */
  private String extractLevel(Level level) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("      \"name\": \"").append(escapeJson(level.getName())).append("\",\n");
    json.append("      \"elevation\": ").append(level.getElevation()).append(",\n");
    json.append("      \"height\": ").append(level.getHeight()).append(",\n");
    json.append("      \"floorThickness\": ").append(level.getFloorThickness()).append(",\n");
    json.append("      \"visible\": ").append(level.isVisible()).append(",\n");
    json.append("      \"viewable\": ").append(level.isViewable()).append("\n");
    json.append("    }");
    return json.toString();
  }
  
  /**
   * Extracts wall data.
   */
  private String extractWall(Wall wall) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("      \"xStart\": ").append(wall.getXStart()).append(",\n");
    json.append("      \"yStart\": ").append(wall.getYStart()).append(",\n");
    json.append("      \"xEnd\": ").append(wall.getXEnd()).append(",\n");
    json.append("      \"yEnd\": ").append(wall.getYEnd()).append(",\n");
    json.append("      \"thickness\": ").append(wall.getThickness()).append(",\n");
    json.append("      \"height\": ").append(wall.getHeight() != null ? wall.getHeight() : "null").append(",\n");
    json.append("      \"heightAtEnd\": ").append(wall.getHeightAtEnd() != null ? wall.getHeightAtEnd() : "null").append(",\n");
    json.append("      \"arcExtent\": ").append(wall.getArcExtent() != null ? wall.getArcExtent() : "null").append(",\n");
    json.append("      \"leftSideColor\": ").append(wall.getLeftSideColor() != null ? "\"#" + Integer.toHexString(wall.getLeftSideColor()) + "\"" : "null").append(",\n");
    json.append("      \"rightSideColor\": ").append(wall.getRightSideColor() != null ? "\"#" + Integer.toHexString(wall.getRightSideColor()) + "\"" : "null").append(",\n");
    json.append("      \"level\": ").append(wall.getLevel() != null ? "\"" + escapeJson(wall.getLevel().getName()) + "\"" : "null").append("\n");
    json.append("    }");
    return json.toString();
  }
  
  /**
   * Extracts room data.
   */
  private String extractRoom(Room room) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("      \"name\": \"").append(escapeJson(room.getName())).append("\",\n");
    json.append("      \"area\": ").append(room.getArea()).append(",\n");
    json.append("      \"floorColor\": ").append(room.getFloorColor() != null ? "\"#" + Integer.toHexString(room.getFloorColor()) + "\"" : "null").append(",\n");
    json.append("      \"ceilingColor\": ").append(room.getCeilingColor() != null ? "\"#" + Integer.toHexString(room.getCeilingColor()) + "\"" : "null").append(",\n");
    json.append("      \"floorVisible\": ").append(room.isFloorVisible()).append(",\n");
    json.append("      \"ceilingVisible\": ").append(room.isCeilingVisible()).append(",\n");
    json.append("      \"level\": ").append(room.getLevel() != null ? "\"" + escapeJson(room.getLevel().getName()) + "\"" : "null").append("\n");
    json.append("    }");
    return json.toString();
  }
  
  /**
   * Extracts furniture data.
   */
  private String extractFurniture(HomePieceOfFurniture piece) {
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    json.append("      \"name\": \"").append(escapeJson(piece.getName())).append("\",\n");
    json.append("      \"description\": \"").append(escapeJson(piece.getDescription())).append("\",\n");
    json.append("      \"x\": ").append(piece.getX()).append(",\n");
    json.append("      \"y\": ").append(piece.getY()).append(",\n");
    json.append("      \"elevation\": ").append(piece.getElevation()).append(",\n");
    json.append("      \"angle\": ").append(piece.getAngle()).append(",\n");
    json.append("      \"width\": ").append(piece.getWidth()).append(",\n");
    json.append("      \"depth\": ").append(piece.getDepth()).append(",\n");
    json.append("      \"height\": ").append(piece.getHeight()).append(",\n");
    json.append("      \"visible\": ").append(piece.isVisible()).append(",\n");
    json.append("      \"movable\": ").append(piece.isMovable()).append(",\n");
    json.append("      \"level\": ").append(piece.getLevel() != null ? "\"" + escapeJson(piece.getLevel().getName()) + "\"" : "null").append("\n");
    json.append("    }");
    return json.toString();
  }
  
  /**
   * Escapes a string for JSON.
   */
  private String escapeJson(String str) {
    if (str == null) return "";
    return str.replace("\\", "\\\\")
              .replace("\"", "\\\"")
              .replace("\n", "\\n")
              .replace("\r", "\\r")
              .replace("\t", "\\t");
  }
}

</file>
<file path="OpenAICompatibleClient.java">
/*
 * OpenAICompatibleClient.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

/**
 * OpenAI-compatible client implementation using the Chat Completions API.
 * This implementation uses Java's built-in HTTP capabilities and supports
 * all OpenAI-compatible providers including commercial and local services.
 * 
 * <AUTHOR> Kpassegna
 */
public class OpenAICompatibleClient implements AIClient {
  private final AIProviderConfig config;
  private static final int TIMEOUT_MS = 60000; // 60 seconds
  
  /**
   * Creates a new OpenAI-compatible client.
   */
  public OpenAICompatibleClient(AIProviderConfig config) {
    this.config = config;
  }
  
  @Override
  public CompletableFuture<String> analyzeFloorPlan(String floorPlanData, String prompt) {
    return CompletableFuture.supplyAsync(() -> {
      try {
        return performChatCompletion(floorPlanData, prompt);
      } catch (Exception e) {
        throw new CompletionException("AI analysis failed", e);
      }
    });
  }
  
  /**
   * Performs a chat completion request using the Chat Completions API.
   */
  private String performChatCompletion(String floorPlanData, String prompt) throws IOException {
    String endpoint = config.getBaseUrl();
    if (!endpoint.endsWith("/")) {
      endpoint += "/";
    }
    endpoint += "chat/completions";
    
    URL url = new URL(endpoint);
    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    
    try {
      // Configure connection
      connection.setRequestMethod("POST");
      connection.setRequestProperty("Content-Type", "application/json");
      connection.setRequestProperty("Accept", "application/json");
      connection.setConnectTimeout(TIMEOUT_MS);
      connection.setReadTimeout(TIMEOUT_MS);
      connection.setDoOutput(true);
      
      // Add authorization header if API key is provided
      if (config.getApiKey() != null && !config.getApiKey().isEmpty()) {
        connection.setRequestProperty("Authorization", "Bearer " + config.getApiKey());
      }
      
      // Add custom headers for self-hosted providers
      if (config.getCustomHeaders() != null) {
        for (Map.Entry<String, String> header : config.getCustomHeaders().entrySet()) {
          connection.setRequestProperty(header.getKey(), header.getValue());
        }
      }
      
      // Build request body using Chat Completions API format
      String requestBody = buildChatCompletionRequest(floorPlanData, prompt);
      
      // Send request
      try (OutputStream os = connection.getOutputStream()) {
        byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
        os.write(input, 0, input.length);
      }
      
      // Read response
      int responseCode = connection.getResponseCode();
      if (responseCode == HttpURLConnection.HTTP_OK) {
        return parseSuccessResponse(connection);
      } else {
        throw new IOException("HTTP " + responseCode + ": " + readErrorResponse(connection));
      }
      
    } finally {
      connection.disconnect();
    }
  }
  
  /**
   * Builds the Chat Completions API request body.
   */
  private String buildChatCompletionRequest(String floorPlanData, String prompt) {
    AIModelParameters params = config.getModelParams();
    
    StringBuilder json = new StringBuilder();
    json.append("{");
    json.append("\"model\":\"").append(escapeJson(config.getModel())).append("\",");
    json.append("\"messages\":[");
    json.append("{\"role\":\"system\",\"content\":\"").append(escapeJson(getSystemPrompt())).append("\"},");
    json.append("{\"role\":\"user\",\"content\":\"").append(escapeJson(prompt + "\\n\\nFloor plan data:\\n" + floorPlanData)).append("\"}");
    json.append("],");
    json.append("\"temperature\":").append(params.getTemperature()).append(",");
    json.append("\"max_tokens\":").append(params.getMaxTokens()).append(",");
    json.append("\"top_p\":").append(params.getTopP());
    
    if (params.getFrequencyPenalty() != 0) {
      json.append(",\"frequency_penalty\":").append(params.getFrequencyPenalty());
    }
    if (params.getPresencePenalty() != 0) {
      json.append(",\"presence_penalty\":").append(params.getPresencePenalty());
    }
    
    json.append("}");
    return json.toString();
  }
  
  /**
   * Returns the system prompt for floor plan analysis.
   */
  private String getSystemPrompt() {
    return "You are an expert architect and interior designer with extensive knowledge of " +
           "building codes, design principles, and space optimization. Analyze the provided " +
           "floor plan data and provide comprehensive insights including layout efficiency, " +
           "traffic flow, natural lighting, ventilation, accessibility, and suggestions for " +
           "improvement. Focus on practical recommendations that enhance functionality, " +
           "comfort, and aesthetic appeal.";
  }
  
  /**
   * Parses a successful response from the Chat Completions API.
   */
  private String parseSuccessResponse(HttpURLConnection connection) throws IOException {
    StringBuilder response = new StringBuilder();
    try (BufferedReader reader = new BufferedReader(
        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
      String line;
      while ((line = reader.readLine()) != null) {
        response.append(line);
      }
    }
    
    // Parse JSON response to extract the content
    String jsonResponse = response.toString();
    return extractContentFromResponse(jsonResponse);
  }
  
  /**
   * Extracts the content from a Chat Completions API response.
   */
  private String extractContentFromResponse(String jsonResponse) {
    // Simple JSON parsing to extract content from choices[0].message.content
    // This is a basic implementation - in a production system, you'd use a proper JSON library
    try {
      int choicesStart = jsonResponse.indexOf("\"choices\":");
      if (choicesStart == -1) return "Unable to parse response";
      
      int contentStart = jsonResponse.indexOf("\"content\":", choicesStart);
      if (contentStart == -1) return "No content found in response";
      
      contentStart = jsonResponse.indexOf("\"", contentStart + 10) + 1;
      int contentEnd = findJsonStringEnd(jsonResponse, contentStart);
      
      if (contentEnd > contentStart) {
        return unescapeJson(jsonResponse.substring(contentStart, contentEnd));
      }
      
      return "Unable to extract content from response";
    } catch (Exception e) {
      return "Error parsing response: " + e.getMessage();
    }
  }
  
  @Override
  public boolean testConnection() {
    try {
      // Simple test by making a minimal request
      String testPrompt = "Hello";
      String testData = "{}";
      performChatCompletion(testData, testPrompt);
      return true;
    } catch (Exception e) {
      return false;
    }
  }
  
  @Override
  public List<String> getAvailableModels() {
    // For now, return the configured model
    // In a full implementation, this would query the /models endpoint
    List<String> models = new ArrayList<>();
    if (config.getModel() != null && !config.getModel().isEmpty()) {
      models.add(config.getModel());
    }
    return models;
  }
  
  @Override
  public void close() {
    // No resources to close in this implementation
  }
  
  /**
   * Reads error response from the connection.
   */
  private String readErrorResponse(HttpURLConnection connection) {
    try (BufferedReader reader = new BufferedReader(
        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
      StringBuilder error = new StringBuilder();
      String line;
      while ((line = reader.readLine()) != null) {
        error.append(line);
      }
      return error.toString();
    } catch (Exception e) {
      return "Unknown error";
    }
  }
  
  /**
   * Escapes a string for JSON.
   */
  private String escapeJson(String str) {
    if (str == null) return "";
    return str.replace("\\", "\\\\")
              .replace("\"", "\\\"")
              .replace("\n", "\\n")
              .replace("\r", "\\r")
              .replace("\t", "\\t");
  }
  
  /**
   * Unescapes a JSON string.
   */
  private String unescapeJson(String str) {
    if (str == null) return "";
    return str.replace("\\\"", "\"")
              .replace("\\\\", "\\")
              .replace("\\n", "\n")
              .replace("\\r", "\r")
              .replace("\\t", "\t");
  }
  
  /**
   * Finds the end of a JSON string value.
   */
  private int findJsonStringEnd(String json, int start) {
    for (int i = start; i < json.length(); i++) {
      char c = json.charAt(i);
      if (c == '"' && (i == start || json.charAt(i - 1) != '\\')) {
        return i;
      }
    }
    return json.length();
  }
}

</file>
<file path="PrivacyManager.java">
/*
 * PrivacyManager.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import javax.swing.JCheckBox;
import javax.swing.JOptionPane;
import java.awt.Component;
import java.util.regex.Pattern;

/**
 * Manages privacy settings and data sanitization for AI analysis.
 * Provides options to exclude personal information from floor plan data.
 * 
 * <AUTHOR> Kpassegna
 */
public class PrivacyManager {
  
  // Patterns for detecting potentially personal information
  private static final Pattern NAME_PATTERN = Pattern.compile("\"name\"\\s*:\\s*\"[^\"]*\"");
  private static final Pattern DESCRIPTION_PATTERN = Pattern.compile("\"description\"\\s*:\\s*\"[^\"]*\"");
  
  /**
   * Sanitizes floor plan data by removing or anonymizing personal information.
   * 
   * @param floorPlanData The original floor plan data
   * @param includePersonalInfo Whether to include personal information
   * @return Sanitized floor plan data
   */
  public String sanitizeFloorPlanData(String floorPlanData, boolean includePersonalInfo) {
    if (includePersonalInfo) {
      return floorPlanData;
    }
    
    String sanitized = floorPlanData;
    
    // Replace specific names with generic terms
    sanitized = NAME_PATTERN.matcher(sanitized).replaceAll("\"name\":\"Room\"");
    
    // Replace descriptions with generic terms
    sanitized = DESCRIPTION_PATTERN.matcher(sanitized).replaceAll("\"description\":\"Furniture\"");
    
    // Remove any custom properties that might contain personal data
    sanitized = removeCustomProperties(sanitized);
    
    return sanitized;
  }
  
  /**
   * Shows a privacy notice dialog and returns user preferences.
   * 
   * @param parent The parent component for the dialog
   * @return true if the user wants to exclude personal information, false otherwise
   */
  public boolean showPrivacyNotice(Component parent) {
    String message = "This feature will send your floor plan data to the configured AI service.\n" +
                    "Please ensure you trust the AI provider with your data.\n\n" +
                    "Personal information (room names, furniture descriptions) can be excluded from the analysis.";
    
    JCheckBox excludePersonalInfo = new JCheckBox("Exclude personal information (recommended)", true);
    Object[] components = {message, excludePersonalInfo};
    
    int result = JOptionPane.showConfirmDialog(parent, components,
        "Privacy Notice", JOptionPane.OK_CANCEL_OPTION, JOptionPane.INFORMATION_MESSAGE);
    
    if (result == JOptionPane.OK_OPTION) {
      return excludePersonalInfo.isSelected();
    } else {
      // User cancelled, assume they want privacy
      return true;
    }
  }
  
  /**
   * Shows a privacy notice for first-time users.
   * 
   * @param parent The parent component for the dialog
   * @return true if user agrees to proceed, false if they cancel
   */
  public boolean showFirstTimePrivacyNotice(Component parent) {
    String message = "Welcome to AI Floor Plan Analysis!\n\n" +
                    "This feature analyzes your floor plan using artificial intelligence to provide\n" +
                    "insights and suggestions for improvement.\n\n" +
                    "Important Privacy Information:\n" +
                    "• Your floor plan data will be sent to your configured AI provider\n" +
                    "• No data is stored permanently by the AI service\n" +
                    "• You can exclude personal information (names, descriptions)\n" +
                    "• Local AI providers (Ollama, LM Studio) keep data on your computer\n\n" +
                    "Do you want to proceed with the analysis?";
    
    JCheckBox excludePersonalInfo = new JCheckBox("Exclude personal information from analysis", true);
    JCheckBox dontShowAgain = new JCheckBox("Don't show this notice again", false);
    
    Object[] components = {message, excludePersonalInfo, dontShowAgain};
    
    int result = JOptionPane.showConfirmDialog(parent, components,
        "AI Analysis Privacy Notice", JOptionPane.YES_NO_OPTION, JOptionPane.INFORMATION_MESSAGE);
    
    if (result == JOptionPane.YES_OPTION) {
      // Store user preferences
      storePrivacyPreferences(excludePersonalInfo.isSelected(), dontShowAgain.isSelected());
      return true;
    }
    
    return false;
  }
  
  /**
   * Checks if the privacy notice should be shown.
   * 
   * @return true if the notice should be shown, false otherwise
   */
  public boolean shouldShowPrivacyNotice() {
    // Check if user has opted out of seeing the notice
    java.util.prefs.Preferences prefs = java.util.prefs.Preferences.userNodeForPackage(PrivacyManager.class);
    return !prefs.getBoolean("privacyNoticeShown", false);
  }
  
  /**
   * Gets the user's preference for excluding personal information.
   * 
   * @return true if personal information should be excluded, false otherwise
   */
  public boolean shouldExcludePersonalInfo() {
    java.util.prefs.Preferences prefs = java.util.prefs.Preferences.userNodeForPackage(PrivacyManager.class);
    return prefs.getBoolean("excludePersonalInfo", true); // Default to true for privacy
  }
  
  /**
   * Stores user privacy preferences.
   * 
   * @param excludePersonalInfo Whether to exclude personal information
   * @param dontShowAgain Whether to show the privacy notice again
   */
  private void storePrivacyPreferences(boolean excludePersonalInfo, boolean dontShowAgain) {
    java.util.prefs.Preferences prefs = java.util.prefs.Preferences.userNodeForPackage(PrivacyManager.class);
    prefs.putBoolean("excludePersonalInfo", excludePersonalInfo);
    if (dontShowAgain) {
      prefs.putBoolean("privacyNoticeShown", true);
    }
  }
  
  /**
   * Removes custom properties from JSON data that might contain personal information.
   * 
   * @param jsonData The JSON data to sanitize
   * @return Sanitized JSON data
   */
  private String removeCustomProperties(String jsonData) {
    // Simple implementation - in a production system, you'd use a proper JSON parser
    // This removes any "properties" objects that might contain custom user data
    return jsonData.replaceAll("\"properties\"\\s*:\\s*\\{[^}]*\\}", "\"properties\":{}");
  }
  
  /**
   * Validates that the data doesn't contain obvious personal information.
   * 
   * @param data The data to validate
   * @return true if the data appears to be sanitized, false otherwise
   */
  public boolean validateDataPrivacy(String data) {
    // Check for common personal information patterns
    String lowerData = data.toLowerCase();
    
    // Check for email addresses
    if (lowerData.matches(".*\\b[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}\\b.*")) {
      return false;
    }
    
    // Check for phone numbers (simple pattern)
    if (lowerData.matches(".*\\b\\d{3}[-.]?\\d{3}[-.]?\\d{4}\\b.*")) {
      return false;
    }
    
    // Check for common personal names in room descriptions
    String[] personalTerms = {"bedroom", "john", "mary", "master", "guest", "office"};
    for (String term : personalTerms) {
      if (lowerData.contains("\"" + term + "\"")) {
        // This might be personal information, but it's also common architectural terminology
        // We'll allow it but could flag for review
      }
    }
    
    return true;
  }
}

</file>
<file path="SecureConfigStorage.java">
/*
 * SecureConfigStorage.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.Arrays;
import java.util.Base64;
import java.util.prefs.Preferences;

/**
 * Provides secure storage for sensitive configuration data like API keys.
 * Uses basic obfuscation for protection while maintaining compatibility.
 * 
 * <AUTHOR> Kpassegna
 */
public class SecureConfigStorage {
  private static final String API_KEY_PREF = "aiApiKeyObfuscated";
  private static final byte[] OBFUSCATION_KEY = {0x53, 0x48, 0x33, 0x44, 0x41, 0x49}; // "SH3DAI"
  
  private final Preferences preferences;
  
  /**
   * Creates a new secure config storage instance.
   */
  public SecureConfigStorage() {
    this.preferences = Preferences.userNodeForPackage(SecureConfigStorage.class);
  }
  
  /**
   * Stores an API key using basic obfuscation.
   */
  public void storeApiKey(String apiKey) {
    if (apiKey == null || apiKey.isEmpty()) {
      clearApiKey();
      return;
    }
    
    try {
      // Simple obfuscation (not encryption) for basic protection
      byte[] obfuscated = obfuscate(apiKey.getBytes("UTF-8"));
      String encoded = Base64.getEncoder().encodeToString(obfuscated);
      preferences.put(API_KEY_PREF, encoded);
    } catch (Exception e) {
      // Fallback to plain storage if obfuscation fails
      preferences.put(API_KEY_PREF, apiKey);
    }
  }
  
  /**
   * Retrieves the stored API key.
   */
  public String retrieveApiKey() {
    String stored = preferences.get(API_KEY_PREF, "");
    if (stored.isEmpty()) {
      return "";
    }
    
    try {
      // Try to decode as obfuscated data
      byte[] decoded = Base64.getDecoder().decode(stored);
      byte[] deobfuscated = deobfuscate(decoded);
      return new String(deobfuscated, "UTF-8");
    } catch (Exception e) {
      // Fallback: assume it's plain text (for backward compatibility)
      return stored;
    }
  }
  
  /**
   * Clears the stored API key.
   */
  public void clearApiKey() {
    preferences.remove(API_KEY_PREF);
  }
  
  /**
   * Simple obfuscation using XOR with a key.
   */
  private byte[] obfuscate(byte[] data) {
    byte[] result = new byte[data.length];
    for (int i = 0; i < data.length; i++) {
      result[i] = (byte) (data[i] ^ OBFUSCATION_KEY[i % OBFUSCATION_KEY.length]);
    }
    return result;
  }
  
  /**
   * Deobfuscation (same as obfuscation for XOR).
   */
  private byte[] deobfuscate(byte[] data) {
    return obfuscate(data); // XOR is its own inverse
  }
}

</file>
<file path="ValidationResult.java">
/*
 * ValidationResult.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel Kpassegna
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.Collections;
import java.util.List;

/**
 * Result of configuration validation.
 * 
 * <AUTHOR> Kpassegna
 */
public class ValidationResult {
  private final boolean valid;
  private final List<String> errors;
  
  /**
   * Creates a validation result.
   */
  public ValidationResult(boolean valid, List<String> errors) {
    this.valid = valid;
    this.errors = errors != null ? errors : Collections.emptyList();
  }
  
  /**
   * Returns whether the validation passed.
   */
  public boolean isValid() {
    return valid;
  }
  
  /**
   * Returns the list of validation errors.
   */
  public List<String> getErrors() {
    return Collections.unmodifiableList(errors);
  }
  
  /**
   * Returns the first error message, or null if no errors.
   */
  public String getFirstError() {
    return errors.isEmpty() ? null : errors.get(0);
  }
  
  @Override
  public String toString() {
    return "ValidationResult{" +
           "valid=" + valid +
           ", errors=" + errors +
           '}';
  }
}

</file>