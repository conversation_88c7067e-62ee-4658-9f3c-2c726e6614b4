/*
 * ValidationResult.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents the result of a validation operation.
 * Contains information about whether validation passed and any error messages.
 * 
 * <AUTHOR>
 */
public class ValidationResult {
  private final boolean valid;
  private final List<String> errorMessages;
  
  /**
   * Creates a successful validation result.
   */
  public ValidationResult() {
    this.valid = true;
    this.errorMessages = new ArrayList<>();
  }
  
  /**
   * Creates a validation result with the specified validity and error messages.
   */
  public ValidationResult(boolean valid, List<String> errorMessages) {
    this.valid = valid;
    this.errorMessages = errorMessages != null ? new ArrayList<>(errorMessages) : new ArrayList<>();
  }
  
  /**
   * Creates a failed validation result with a single error message.
   */
  public ValidationResult(String errorMessage) {
    this.valid = false;
    this.errorMessages = new ArrayList<>();
    this.errorMessages.add(errorMessage);
  }
  
  /**
   * Returns whether the validation was successful.
   */
  public boolean isValid() {
    return valid;
  }
  
  /**
   * Returns the list of error messages.
   */
  public List<String> getErrorMessages() {
    return new ArrayList<>(errorMessages);
  }
  
  /**
   * Returns a single error message combining all error messages.
   */
  public String getErrorMessage() {
    if (errorMessages.isEmpty()) {
      return "";
    }
    
    if (errorMessages.size() == 1) {
      return errorMessages.get(0);
    }
    
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < errorMessages.size(); i++) {
      if (i > 0) {
        sb.append("\n");
      }
      sb.append("• ").append(errorMessages.get(i));
    }
    return sb.toString();
  }
  
  /**
   * Creates a successful validation result.
   */
  public static ValidationResult success() {
    return new ValidationResult();
  }
  
  /**
   * Creates a failed validation result with a single error message.
   */
  public static ValidationResult failure(String errorMessage) {
    return new ValidationResult(errorMessage);
  }
  
  /**
   * Creates a failed validation result with multiple error messages.
   */
  public static ValidationResult failure(List<String> errorMessages) {
    return new ValidationResult(false, errorMessages);
  }
  
  @Override
  public String toString() {
    return "ValidationResult{" +
           "valid=" + valid +
           ", errorMessages=" + errorMessages +
           '}';
  }
}
