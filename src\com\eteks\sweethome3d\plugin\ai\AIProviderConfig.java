/*
 * AIProviderConfig.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>na
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Configuration for AI providers supporting OpenAI-compatible endpoints.
 * This class stores all necessary information to connect to various AI services
 * including commercial providers, API aggregators, and local LLM tools.
 * 
 * <AUTHOR>
 */
public class AIProviderConfig implements Serializable {
  private static final long serialVersionUID = 1L;
  
  private String providerName;
  private String baseUrl;
  private String apiKey;
  private String model;
  private Map<String, String> customHeaders;
  private AIModelParameters modelParams;
  
  /**
   * Creates a new AI provider configuration.
   */
  public AIProviderConfig() {
    this.customHeaders = new HashMap<>();
    this.modelParams = new AIModelParameters();
  }
  
  /**
   * Creates a new AI provider configuration with the specified parameters.
   */
  public AIProviderConfig(String providerName, String baseUrl, String apiKey, String model) {
    this();
    this.providerName = providerName;
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
    this.model = model;
  }
  
  /**
   * Returns the display name of the provider.
   */
  public String getProviderName() {
    return providerName;
  }
  
  /**
   * Sets the display name of the provider.
   */
  public void setProviderName(String providerName) {
    this.providerName = providerName;
  }
  
  /**
   * Returns the base URL of the API endpoint.
   */
  public String getBaseUrl() {
    return baseUrl;
  }
  
  /**
   * Sets the base URL of the API endpoint.
   */
  public void setBaseUrl(String baseUrl) {
    this.baseUrl = baseUrl;
  }
  
  /**
   * Returns the API key for authentication.
   */
  public String getApiKey() {
    return apiKey;
  }
  
  /**
   * Sets the API key for authentication.
   */
  public void setApiKey(String apiKey) {
    this.apiKey = apiKey;
  }
  
  /**
   * Returns the model identifier.
   */
  public String getModel() {
    return model;
  }
  
  /**
   * Sets the model identifier.
   */
  public void setModel(String model) {
    this.model = model;
  }
  
  /**
   * Returns custom headers for self-hosted providers.
   */
  public Map<String, String> getCustomHeaders() {
    return customHeaders;
  }
  
  /**
   * Sets custom headers for self-hosted providers.
   */
  public void setCustomHeaders(Map<String, String> customHeaders) {
    this.customHeaders = customHeaders != null ? customHeaders : new HashMap<>();
  }
  
  /**
   * Returns the model parameters.
   */
  public AIModelParameters getModelParams() {
    return modelParams;
  }
  
  /**
   * Sets the model parameters.
   */
  public void setModelParams(AIModelParameters modelParams) {
    this.modelParams = modelParams != null ? modelParams : new AIModelParameters();
  }
  
  /**
   * Creates a new builder for AIProviderConfig.
   */
  public static Builder builder() {
    return new Builder();
  }
  
  /**
   * Builder pattern implementation for AIProviderConfig.
   */
  public static class Builder {
    private AIProviderConfig config;
    
    public Builder() {
      this.config = new AIProviderConfig();
    }
    
    public Builder providerName(String providerName) {
      config.setProviderName(providerName);
      return this;
    }
    
    public Builder baseUrl(String baseUrl) {
      config.setBaseUrl(baseUrl);
      return this;
    }
    
    public Builder apiKey(String apiKey) {
      config.setApiKey(apiKey);
      return this;
    }
    
    public Builder model(String model) {
      config.setModel(model);
      return this;
    }
    
    public Builder customHeaders(Map<String, String> customHeaders) {
      config.setCustomHeaders(customHeaders);
      return this;
    }
    
    public Builder modelParams(AIModelParameters modelParams) {
      config.setModelParams(modelParams);
      return this;
    }
    
    public AIProviderConfig build() {
      return config;
    }
  }
  
  @Override
  public String toString() {
    return "AIProviderConfig{" +
           "providerName='" + providerName + '\'' +
           ", baseUrl='" + baseUrl + '\'' +
           ", model='" + model + '\'' +
           ", hasApiKey=" + (apiKey != null && !apiKey.isEmpty()) +
           '}';
  }
}
