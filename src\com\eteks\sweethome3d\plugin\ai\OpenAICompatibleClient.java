/*
 * OpenAICompatibleClient.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>na
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

/**
 * OpenAI-compatible client implementation using the Chat Completions API.
 * This implementation uses Java's built-in HTTP capabilities and supports
 * all OpenAI-compatible providers including commercial and local services.
 * 
 * <AUTHOR> Kpassegna
 */
public class OpenAICompatibleClient implements AIClient {
  private final AIProviderConfig config;
  private static final int TIMEOUT_MS = 60000; // 60 seconds
  
  /**
   * Creates a new OpenAI-compatible client.
   */
  public OpenAICompatibleClient(AIProviderConfig config) {
    this.config = config;
  }
  
  @Override
  public CompletableFuture<String> analyzeFloorPlan(String floorPlanData, String prompt) {
    return CompletableFuture.supplyAsync(() -> {
      try {
        return performChatCompletion(floorPlanData, prompt);
      } catch (Exception e) {
        throw new CompletionException("AI analysis failed", e);
      }
    });
  }
  
  /**
   * Performs a chat completion request using the Chat Completions API.
   */
  private String performChatCompletion(String floorPlanData, String prompt) throws IOException {
    String endpoint = config.getBaseUrl();
    if (!endpoint.endsWith("/")) {
      endpoint += "/";
    }
    endpoint += "chat/completions";
    
    URL url = new URL(endpoint);
    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    
    try {
      // Configure connection
      connection.setRequestMethod("POST");
      connection.setRequestProperty("Content-Type", "application/json");
      connection.setRequestProperty("Accept", "application/json");
      connection.setConnectTimeout(TIMEOUT_MS);
      connection.setReadTimeout(TIMEOUT_MS);
      connection.setDoOutput(true);
      
      // Add authorization header if API key is provided
      if (config.getApiKey() != null && !config.getApiKey().isEmpty()) {
        connection.setRequestProperty("Authorization", "Bearer " + config.getApiKey());
      }
      
      // Add custom headers for self-hosted providers
      if (config.getCustomHeaders() != null) {
        for (Map.Entry<String, String> header : config.getCustomHeaders().entrySet()) {
          connection.setRequestProperty(header.getKey(), header.getValue());
        }
      }
      
      // Build request body using Chat Completions API format
      String requestBody = buildChatCompletionRequest(floorPlanData, prompt);
      
      // Send request
      try (OutputStream os = connection.getOutputStream()) {
        byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
        os.write(input, 0, input.length);
      }
      
      // Read response
      int responseCode = connection.getResponseCode();
      if (responseCode == HttpURLConnection.HTTP_OK) {
        return parseSuccessResponse(connection);
      } else {
        throw new IOException("HTTP " + responseCode + ": " + readErrorResponse(connection));
      }
      
    } finally {
      connection.disconnect();
    }
  }
  
  /**
   * Builds the Chat Completions API request body.
   */
  private String buildChatCompletionRequest(String floorPlanData, String prompt) {
    AIModelParameters params = config.getModelParams();
    
    StringBuilder json = new StringBuilder();
    json.append("{");
    json.append("\"model\":\"").append(escapeJson(config.getModel())).append("\",");
    json.append("\"messages\":[");
    json.append("{\"role\":\"system\",\"content\":\"").append(escapeJson(getSystemPrompt())).append("\"},");
    json.append("{\"role\":\"user\",\"content\":\"").append(escapeJson(prompt + "\\n\\nFloor plan data:\\n" + floorPlanData)).append("\"}");
    json.append("],");
    json.append("\"temperature\":").append(params.getTemperature()).append(",");
    json.append("\"max_tokens\":").append(params.getMaxTokens()).append(",");
    json.append("\"top_p\":").append(params.getTopP());
    
    if (params.getFrequencyPenalty() != 0) {
      json.append(",\"frequency_penalty\":").append(params.getFrequencyPenalty());
    }
    if (params.getPresencePenalty() != 0) {
      json.append(",\"presence_penalty\":").append(params.getPresencePenalty());
    }
    
    json.append("}");
    return json.toString();
  }
  
  /**
   * Returns the system prompt for floor plan analysis.
   */
  private String getSystemPrompt() {
    return "You are an expert architect and interior designer with extensive knowledge of " +
           "building codes, design principles, and space optimization. Analyze the provided " +
           "floor plan data and provide comprehensive insights including layout efficiency, " +
           "traffic flow, natural lighting, ventilation, accessibility, and suggestions for " +
           "improvement. Focus on practical recommendations that enhance functionality, " +
           "comfort, and aesthetic appeal.";
  }
  
  /**
   * Parses a successful response from the Chat Completions API.
   */
  private String parseSuccessResponse(HttpURLConnection connection) throws IOException {
    StringBuilder response = new StringBuilder();
    try (BufferedReader reader = new BufferedReader(
        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
      String line;
      while ((line = reader.readLine()) != null) {
        response.append(line);
      }
    }
    
    // Parse JSON response to extract the content
    String jsonResponse = response.toString();
    return extractContentFromResponse(jsonResponse);
  }
  
  /**
   * Extracts the content from a Chat Completions API response.
   */
  private String extractContentFromResponse(String jsonResponse) {
    // Simple JSON parsing to extract content from choices[0].message.content
    // This is a basic implementation - in a production system, you'd use a proper JSON library
    try {
      int choicesStart = jsonResponse.indexOf("\"choices\":");
      if (choicesStart == -1) return "Unable to parse response";
      
      int contentStart = jsonResponse.indexOf("\"content\":", choicesStart);
      if (contentStart == -1) return "No content found in response";
      
      contentStart = jsonResponse.indexOf("\"", contentStart + 10) + 1;
      int contentEnd = findJsonStringEnd(jsonResponse, contentStart);
      
      if (contentEnd > contentStart) {
        return unescapeJson(jsonResponse.substring(contentStart, contentEnd));
      }
      
      return "Unable to extract content from response";
    } catch (Exception e) {
      return "Error parsing response: " + e.getMessage();
    }
  }
  
  @Override
  public boolean testConnection() {
    try {
      // Simple test by making a minimal request
      String testPrompt = "Hello";
      String testData = "{}";
      performChatCompletion(testData, testPrompt);
      return true;
    } catch (Exception e) {
      return false;
    }
  }
  
  @Override
  public List<String> getAvailableModels() {
    // For now, return the configured model
    // In a full implementation, this would query the /models endpoint
    List<String> models = new ArrayList<>();
    if (config.getModel() != null && !config.getModel().isEmpty()) {
      models.add(config.getModel());
    }
    return models;
  }
  
  @Override
  public void close() {
    // No resources to close in this implementation
  }

  /**
   * Reads error response from the connection.
   */
  private String readErrorResponse(HttpURLConnection connection) {
    try (BufferedReader reader = new BufferedReader(
        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
      StringBuilder response = new StringBuilder();
      String line;
      while ((line = reader.readLine()) != null) {
        response.append(line);
      }
      return response.toString();
    } catch (Exception e) {
      return "Unable to read error response";
    }
  }

  /**
   * Escapes a string for JSON.
   */
  private String escapeJson(String str) {
    if (str == null) return "";
    return str.replace("\\", "\\\\")
              .replace("\"", "\\\"")
              .replace("\n", "\\n")
              .replace("\r", "\\r")
              .replace("\t", "\\t");
  }

  /**
   * Unescapes a JSON string.
   */
  private String unescapeJson(String str) {
    if (str == null) return "";
    return str.replace("\\\"", "\"")
              .replace("\\\\", "\\")
              .replace("\\n", "\n")
              .replace("\\r", "\r")
              .replace("\\t", "\t");
  }

  /**
   * Finds the end of a JSON string value, handling escaped quotes.
   */
  private int findJsonStringEnd(String json, int start) {
    for (int i = start; i < json.length(); i++) {
      char c = json.charAt(i);
      if (c == '"' && (i == start || json.charAt(i - 1) != '\\')) {
        return i;
      }
    }
    return json.length();
  }
}
