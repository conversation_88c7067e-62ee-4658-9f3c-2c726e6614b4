/*
 * AIClientFactory.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

/**
 * Factory for creating AI clients based on provider configuration.
 * 
 * <AUTHOR>
 */
public class AIClientFactory {
  
  /**
   * Creates an AI client for the given configuration.
   * 
   * @param config The provider configuration
   * @return A configured AI client
   * @throws IllegalArgumentException if the configuration is invalid
   */
  public static AIClient createClient(AIProviderConfig config) {
    validateConfig(config);
    return new OpenAICompatibleClient(config);
  }
  
  /**
   * Validates the provider configuration.
   * 
   * @param config The configuration to validate
   * @throws IllegalArgumentException if the configuration is invalid
   */
  private static void validateConfig(AIProviderConfig config) {
    if (config == null) {
      throw new IllegalArgumentException("Configuration cannot be null");
    }
    
    if (config.getBaseUrl() == null || config.getBaseUrl().trim().isEmpty()) {
      throw new IllegalArgumentException("Base URL is required");
    }
    
    if (config.getModel() == null || config.getModel().trim().isEmpty()) {
      throw new IllegalArgumentException("Model is required");
    }
    
    // API key validation is more lenient since local providers might not need it
    String baseUrl = config.getBaseUrl().toLowerCase();
    boolean isLocal = baseUrl.contains("localhost") || 
                     baseUrl.contains("127.0.0.1") || 
                     baseUrl.contains("0.0.0.0");
    
    if (!isLocal && (config.getApiKey() == null || config.getApiKey().trim().isEmpty())) {
      throw new IllegalArgumentException("API Key is required for remote providers");
    }
  }
}
