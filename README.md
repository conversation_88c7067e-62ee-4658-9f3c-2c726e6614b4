# AI Floor Plan Analysis Plugin for Sweet Home 3D

A comprehensive AI-powered plugin for Sweet Home 3D that provides intelligent floor plan analysis and design recommendations using modern AI services.

## Features

- **AI-Powered Analysis**: Comprehensive floor plan analysis using state-of-the-art AI models
- **Multi-Provider Support**: Compatible with OpenAI, Anthropic, Google AI, xAI, and many other providers
- **Local AI Support**: Works with local LLM tools like Ollama, LM Studio, AnythingLLM, and Jan
- **Interactive Chat Interface**: Conversational AI interface for asking questions about your design
- **Privacy Controls**: Configurable privacy settings to control what data is shared with AI services
- **Comprehensive Data Extraction**: Analyzes all floor plan elements including compass data, walls, rooms, furniture, and spatial relationships
- **Multilingual Support**: Available in English, French, and German

## Supported AI Providers

### Commercial Providers
- **OpenAI** (GPT-4, GPT-5, o3, o4-mini)
- **Anthropic** (Claude Opus 4.1, Claude Sonnet 4, Claude 3.5 Sonnet)
- **Google AI** (Gemini 2.5 Pro, Gemini 2.0 Flash)
- **xAI** (<PERSON><PERSON>-4, <PERSON>rok-3, <PERSON>rok-2)

### API Aggregators
- **Together AI**
- **Fireworks AI**
- **OpenRouter**
- **Groq**
- **DeepInfra**

### Local & Self-Hosted
- **Ollama** (localhost:11434)
- **LM Studio** (localhost:1234)
- **AnythingLLM** (localhost:3001)
- **Jan** (localhost:1337)

## Installation

### Prerequisites
- Sweet Home 3D 7.5 or later
- Java 8 or later
- Internet connection (for cloud AI providers) or local AI setup

### Building the Plugin

1. **Clone or download this project**
2. **Ensure SweetHome3D-7.5.jar is in the project root directory**
3. **Run the build script:**
   ```bash
   # On Windows
   test-plugin.bat
   
   # On Linux/Mac
   chmod +x test-plugin.sh
   ./test-plugin.sh
   ```

4. **Install the generated JAR file:**
   - Copy `AiFloorPlanAnalysisPlugin.jar` to your Sweet Home 3D plugins directory
   - On Windows: `%APPDATA%\eTeks\Sweet Home 3D\plugins\`
   - On Mac: `~/Library/Application Support/eTeks/Sweet Home 3D/plugins/`
   - On Linux: `~/.eteks/sweethome3d/plugins/`

5. **Restart Sweet Home 3D**

## Usage

### Initial Setup

1. **Open Sweet Home 3D** and create or open a floor plan
2. **Click the "AI Analysis" button** in the toolbar or go to Tools > AI Analysis
3. **Configure your AI provider** in the settings dialog:
   - Select a provider from the dropdown
   - Enter your API key (if required)
   - Adjust model parameters if needed
   - Configure privacy settings
4. **Test the connection** to ensure everything is working
5. **Save your configuration**

### Analyzing Floor Plans

1. **Create your floor plan** in Sweet Home 3D with walls, rooms, furniture, etc.
2. **Open the AI Analysis dialog** from the toolbar or Tools menu
3. **Click "Analyze Floor Plan"** for a comprehensive analysis
4. **Ask follow-up questions** in the chat interface
5. **Review AI recommendations** for improvements

### Privacy Settings

The plugin includes privacy controls to protect sensitive information:

- **Exclude Personal Information**: Removes names and identifying details
- **Exclude Location Data**: Removes GPS coordinates and timezone information
- **Exclude Furniture Details**: Removes detailed furniture information

## Configuration

### AI Provider Settings

- **Provider**: Select from preset providers or choose "Custom"
- **Base URL**: API endpoint URL
- **API Key**: Your API key for authentication
- **Model**: The AI model to use
- **Temperature**: Controls randomness (0.0-2.0)
- **Max Tokens**: Maximum response length
- **Top P**: Controls nucleus sampling (0.0-1.0)

### Local AI Setup

For local AI providers, no API key is typically required:

1. **Install your preferred local AI tool** (Ollama, LM Studio, etc.)
2. **Start the local AI service**
3. **Select the corresponding provider** in the plugin settings
4. **Verify the base URL** matches your local setup
5. **Test the connection**

## Analysis Features

The AI analyzes your floor plan for:

1. **Layout Efficiency**: Space utilization and room sizing
2. **Traffic Flow**: Circulation patterns and accessibility
3. **Natural Lighting**: Window placement and light distribution
4. **Ventilation**: Air flow and HVAC considerations
5. **Functional Relationships**: Room adjacencies and workflows
6. **Building Standards**: Code compliance and best practices
7. **Energy Efficiency**: Thermal performance and sustainability
8. **Accessibility**: Universal design principles

## Troubleshooting

### Common Issues

**"AI client not configured"**
- Configure your AI provider in the settings dialog
- Ensure your API key is valid
- Test the connection

**"Connection failed"**
- Check your internet connection
- Verify the base URL is correct
- Ensure the AI service is available

**"Authentication failed"**
- Check your API key
- Verify you have permission to use the selected model

**"Model not found"**
- Verify the model name is correct
- Check if the model is available for your account

### Getting Help

1. Check the error message for specific guidance
2. Verify your AI provider settings
3. Test with a different provider if available
4. Check the AI service status page

## Development

### Project Structure

```
src/
├── com/eteks/sweethome3d/plugin/ai/
│   ├── AIPlugin.java                 # Main plugin class
│   ├── AIAction.java                 # Plugin action
│   ├── AIIntegration.java            # Core integration logic
│   ├── AIClient.java                 # AI client interface
│   ├── OpenAICompatibleClient.java   # OpenAI-compatible client
│   ├── AIClientFactory.java          # Client factory
│   ├── AIProviderConfig.java         # Configuration model
│   ├── AIConfigurationManager.java   # Configuration management
│   ├── FloorPlanDataExtractor.java   # Data extraction
│   ├── AIChatDialog.java             # Chat interface
│   ├── AISettingsDialog.java         # Settings dialog
│   └── ... (other classes)
├── ApplicationPlugin.properties       # English localization
├── ApplicationPlugin_fr.properties    # French localization
└── ApplicationPlugin_de.properties    # German localization
```

### Building from Source

1. Ensure Java 8+ and Sweet Home 3D 7.5 JAR are available
2. Compile: `javac -cp SweetHome3D-7.5.jar -d build src/com/eteks/sweethome3d/plugin/ai/*.java`
3. Package: `jar cf AiFloorPlanAnalysisPlugin.jar -C build . -C src ApplicationPlugin*.properties`

## License

This project is licensed under the GNU General Public License v2.0. See the source files for full license text.

## Author

Samuel Kpassegna <<EMAIL>>

## Version History

- **1.0.0** - Initial release with comprehensive AI integration support
