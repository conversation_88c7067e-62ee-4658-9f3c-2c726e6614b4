# ApplicationPlugin.properties
#
# Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# Plugin information
name=AI Floor Plan Analysis Plugin
description=AI-powered floor plan analysis and design recommendations
version=1.0.0
license=GPL v2
provider=<PERSON>

# Plugin class
class=com.eteks.sweethome3d.plugin.ai.AIPlugin

# AI Action localization (English)
AIAction.Name=AI Analysis
AIAction.ShortDescription=Analyze floor plan with AI
AIAction.Menu=Tools

# AI Chat Dialog localization (English)
AIChatDialog.title=AI Floor Plan Analysis
AIChatDialog.welcome=Welcome to AI Floor Plan Analysis! Click 'Analyze Floor Plan' to get started, or ask me any questions about your design.
AIChatDialog.send=Send
AIChatDialog.analyze=Analyze Floor Plan
AIChatDialog.error=Error: 
AIChatDialog.noHome=No floor plan is currently open.
AIChatDialog.analyzing=Analyzing your floor plan...
AIChatDialog.analysisError=Analysis failed: 

# AI Settings Dialog localization (English)
AISettingsDialog.title=AI Settings
AISettingsDialog.providerConfig=Provider Configuration
AISettingsDialog.provider=Provider:
AISettingsDialog.baseUrl=Base URL:
AISettingsDialog.apiKey=API Key:
AISettingsDialog.model=Model:
AISettingsDialog.temperature=Temperature:
AISettingsDialog.maxTokens=Max Tokens:
AISettingsDialog.topP=Top P:
AISettingsDialog.privacySettings=Privacy Settings
AISettingsDialog.excludePersonalInfo=Exclude personal information
AISettingsDialog.excludeLocationData=Exclude location data
AISettingsDialog.excludeFurnitureDetails=Exclude furniture details
AISettingsDialog.testConnection=Test Connection
AISettingsDialog.save=Save
AISettingsDialog.cancel=Cancel
AISettingsDialog.validationError=Configuration Error
AISettingsDialog.error=Error
AISettingsDialog.testing=Testing...
AISettingsDialog.connectionSuccess=Connection successful!
AISettingsDialog.success=Success
AISettingsDialog.connectionFailed=Connection failed.
AISettingsDialog.connectionError=Connection error

# AI Integration localization (English)
analysis.prompt=Please analyze this floor plan and provide comprehensive insights including:\n1. Layout efficiency and space utilization\n2. Traffic flow and circulation patterns\n3. Natural lighting and ventilation opportunities\n4. Accessibility considerations\n5. Functional relationships between spaces\n6. Suggestions for improvement\n7. Compliance with common building standards\n8. Energy efficiency considerations\n\nPlease provide specific, actionable recommendations that would enhance the functionality, comfort, and aesthetic appeal of this space.
