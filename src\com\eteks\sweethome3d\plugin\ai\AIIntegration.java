/*
 * AIIntegration.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.UserPreferences;
import java.util.concurrent.CompletableFuture;

/**
 * Main integration class that handles data extraction from Sweet Home 3D
 * and communication with AI services for floor plan analysis.
 *
 * <AUTHOR> <PERSON>
 */
public class AIIntegration {
  private final AIConfigurationManager configManager;
  private final PrivacyManager privacyManager;
  private final UserPreferences preferences;
  private AIClient aiClient;

  /**
   * Creates a new AI integration instance.
   *
   * @param preferences user preferences for accessing localized strings
   */
  public AIIntegration(UserPreferences preferences) {
    this.preferences = preferences;
    this.configManager = new AIConfigurationManager();
    this.privacyManager = new PrivacyManager();
    initializeClient();
  }
  
  /**
   * Initializes the AI client from saved configuration.
   */
  private void initializeClient() {
    AIProviderConfig config = configManager.loadConfiguration();
    if (config != null && isConfigurationValid(config)) {
      try {
        this.aiClient = AIClientFactory.createClient(config);
      } catch (Exception e) {
        // Client initialization failed, will be null
        this.aiClient = null;
      }
    }
  }
  
  /**
   * Analyzes a home's floor plan using AI.
   *
   * @param home The home to analyze
   * @return A CompletableFuture containing the AI analysis
   */
  public CompletableFuture<String> analyzeHome(Home home) {
    if (aiClient == null) {
      throw new IllegalStateException("AI client not configured. Please configure AI settings first.");
    }

    String floorPlanData = extractFloorPlanData(home);
    String analysisPrompt = buildAnalysisPrompt();

    return aiClient.analyzeFloorPlan(floorPlanData, analysisPrompt);
  }

  /**
   * Asks a follow-up question about the floor plan.
   *
   * @param home The home to analyze
   * @param question The follow-up question
   * @return A CompletableFuture containing the AI response
   */
  public CompletableFuture<String> askQuestion(Home home, String question) {
    if (aiClient == null) {
      throw new IllegalStateException("AI client not configured. Please configure AI settings first.");
    }

    String floorPlanData = extractFloorPlanData(home);
    return aiClient.analyzeFloorPlan(floorPlanData, question);
  }
  
  /**
   * Extracts floor plan data from a Home object and serializes it to JSON.
   *
   * @param home The home to extract data from
   * @return JSON representation of the floor plan data
   */
  public String extractFloorPlanData(Home home) {
    FloorPlanDataExtractor extractor = new FloorPlanDataExtractor();
    String rawData = extractor.extractToJson(home);

    // Apply privacy settings
    boolean excludePersonalInfo = privacyManager.shouldExcludePersonalInfo();
    return privacyManager.sanitizeFloorPlanData(rawData, !excludePersonalInfo);
  }
  
  /**
   * Builds the analysis prompt for the AI using localized strings.
   *
   * @return The analysis prompt
   */
  private String buildAnalysisPrompt() {
    try {
      return preferences.getLocalizedString(AIIntegration.class, "analysis.prompt");
    } catch (IllegalArgumentException ex) {
      // Fallback to English if localized prompt is not found
      return "Please analyze this floor plan and provide comprehensive insights including:\n" +
             "1. Layout efficiency and space utilization\n" +
             "2. Traffic flow and circulation patterns\n" +
             "3. Natural lighting and ventilation opportunities\n" +
             "4. Accessibility considerations\n" +
             "5. Functional relationships between spaces\n" +
             "6. Suggestions for improvement\n" +
             "7. Compliance with common building standards\n" +
             "8. Energy efficiency considerations\n\n" +
             "Please provide specific, actionable recommendations that would enhance " +
             "the functionality, comfort, and aesthetic appeal of this space.";
    }
  }
  
  /**
   * Checks if the AI client is properly configured.
   * 
   * @return true if configured, false otherwise
   */
  public boolean isConfigured() {
    return aiClient != null;
  }
  
  /**
   * Reconfigures the AI client with current settings.
   */
  public void reconfigure() {
    if (aiClient != null) {
      aiClient.close();
    }
    initializeClient();
  }
  
  /**
   * Tests the connection to the AI service.
   * 
   * @return true if connection is successful, false otherwise
   */
  public boolean testConnection() {
    return aiClient != null && aiClient.testConnection();
  }
  
  /**
   * Gets the current configuration.
   * 
   * @return The current AI provider configuration, or null if not configured
   */
  public AIProviderConfig getCurrentConfiguration() {
    return configManager.loadConfiguration();
  }
  
  /**
   * Saves a new configuration.
   * 
   * @param config The configuration to save
   */
  public void saveConfiguration(AIProviderConfig config) {
    configManager.saveConfiguration(config);
    reconfigure();
  }
  
  /**
   * Validates a configuration.
   * 
   * @param config The configuration to validate
   * @return true if valid, false otherwise
   */
  private boolean isConfigurationValid(AIProviderConfig config) {
    if (config == null) {
      return false;
    }
    
    AIConfigValidator validator = new AIConfigValidator();
    ValidationResult result = validator.validate(config);
    return result.isValid();
  }
  
  /**
   * Gets the privacy manager.
   *
   * @return The privacy manager instance
   */
  public PrivacyManager getPrivacyManager() {
    return privacyManager;
  }

  /**
   * Closes the AI integration and releases resources.
   */
  public void close() {
    if (aiClient != null) {
      aiClient.close();
      aiClient = null;
    }
  }
}
