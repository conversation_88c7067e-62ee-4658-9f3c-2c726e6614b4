/*
 * FloorPlanDataExtractor.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.*;
import java.util.Collection;
import java.util.List;

/**
 * Extracts comprehensive floor plan data from Sweet Home 3D models.
 * Converts all relevant home elements and their properties to JSON format
 * for AI analysis, including compass data and spatial relationships.
 * 
 * <AUTHOR>
 */
public class FloorPlanDataExtractor {
  
  /**
   * Extracts all floor plan data from a Home object and returns it as JSON.
   * 
   * @param home The Sweet Home 3D home to extract data from
   * @return JSON representation of the floor plan data
   */
  public String extractToJson(Home home) {
    if (home == null) {
      return "{}";
    }
    
    StringBuilder json = new StringBuilder();
    json.append("{\n");
    
    // Basic home information
    appendHomeInfo(json, home);
    json.append(",\n");
    
    // Compass information
    appendCompassInfo(json, home);
    json.append(",\n");
    
    // Walls
    appendWalls(json, home.getWalls());
    json.append(",\n");
    
    // Rooms
    appendRooms(json, home.getRooms());
    json.append(",\n");
    
    // Furniture and objects
    appendFurniture(json, home.getFurniture());
    json.append(",\n");
    
    // Doors and windows (extract from furniture)
    appendDoorsAndWindows(json, home.getFurniture());
    json.append(",\n");
    
    // Levels
    appendLevels(json, home.getLevels());
    json.append(",\n");
    
    // Environment settings
    appendEnvironment(json, home);
    
    json.append("\n}");
    return json.toString();
  }
  
  /**
   * Appends basic home information to the JSON.
   */
  private void appendHomeInfo(StringBuilder json, Home home) {
    json.append("  \"homeInfo\": {\n");
    json.append("    \"name\": \"").append(escapeJson(home.getName())).append("\",\n");
    json.append("    \"version\": \"").append(String.valueOf(home.getVersion())).append("\",\n");
    json.append("    \"wallHeight\": ").append(home.getWallHeight()).append(",\n");
    json.append("    \"selectedLevel\": \"").append(home.getSelectedLevel() != null ? escapeJson(home.getSelectedLevel().getName()) : "").append("\"\n");
    json.append("  }");
  }
  
  /**
   * Appends compass information to the JSON.
   */
  private void appendCompassInfo(StringBuilder json, Home home) {
    json.append("  \"compass\": {\n");
    Compass compass = home.getCompass();
    if (compass != null) {
      json.append("    \"x\": ").append(compass.getX()).append(",\n");
      json.append("    \"y\": ").append(compass.getY()).append(",\n");
      json.append("    \"diameter\": ").append(compass.getDiameter()).append(",\n");
      json.append("    \"northDirection\": ").append(compass.getNorthDirection()).append(",\n");
      json.append("    \"latitude\": ").append(compass.getLatitude()).append(",\n");
      json.append("    \"longitude\": ").append(compass.getLongitude()).append(",\n");
      json.append("    \"timeZone\": \"").append(escapeJson(compass.getTimeZone())).append("\",\n");
      json.append("    \"visible\": ").append(compass.isVisible()).append("\n");
    } else {
      json.append("    \"present\": false\n");
    }
    json.append("  }");
  }
  
  /**
   * Appends wall information to the JSON.
   */
  private void appendWalls(StringBuilder json, Collection<Wall> walls) {
    json.append("  \"walls\": [\n");
    boolean first = true;
    for (Wall wall : walls) {
      if (!first) json.append(",\n");
      first = false;
      
      json.append("    {\n");
      json.append("      \"id\": \"").append(wall.hashCode()).append("\",\n");
      json.append("      \"xStart\": ").append(wall.getXStart()).append(",\n");
      json.append("      \"yStart\": ").append(wall.getYStart()).append(",\n");
      json.append("      \"xEnd\": ").append(wall.getXEnd()).append(",\n");
      json.append("      \"yEnd\": ").append(wall.getYEnd()).append(",\n");
      json.append("      \"thickness\": ").append(wall.getThickness()).append(",\n");
      json.append("      \"height\": ").append(wall.getHeight() != null ? wall.getHeight() : "null").append(",\n");
      json.append("      \"heightAtEnd\": ").append(wall.getHeightAtEnd() != null ? wall.getHeightAtEnd() : "null").append(",\n");
      json.append("      \"arcExtent\": ").append(wall.getArcExtent() != null ? wall.getArcExtent() : "null").append(",\n");
      json.append("      \"leftSideColor\": ").append(wall.getLeftSideColor() != null ? wall.getLeftSideColor() : "null").append(",\n");
      json.append("      \"rightSideColor\": ").append(wall.getRightSideColor() != null ? wall.getRightSideColor() : "null").append(",\n");
      json.append("      \"pattern\": \"").append(wall.getPattern() != null ? escapeJson(wall.getPattern().getName()) : "").append("\",\n");
      json.append("      \"level\": \"").append(wall.getLevel() != null ? escapeJson(wall.getLevel().getName()) : "").append("\"\n");
      json.append("    }");
    }
    json.append("\n  ]");
  }
  
  /**
   * Appends room information to the JSON.
   */
  private void appendRooms(StringBuilder json, Collection<Room> rooms) {
    json.append("  \"rooms\": [\n");
    boolean first = true;
    for (Room room : rooms) {
      if (!first) json.append(",\n");
      first = false;
      
      json.append("    {\n");
      json.append("      \"id\": \"").append(room.hashCode()).append("\",\n");
      json.append("      \"name\": \"").append(escapeJson(room.getName())).append("\",\n");
      json.append("      \"area\": ").append(room.getArea()).append(",\n");
      json.append("      \"floorColor\": ").append(room.getFloorColor() != null ? room.getFloorColor() : "null").append(",\n");
      json.append("      \"ceilingColor\": ").append(room.getCeilingColor() != null ? room.getCeilingColor() : "null").append(",\n");
      json.append("      \"floorVisible\": ").append(room.isFloorVisible()).append(",\n");
      json.append("      \"ceilingVisible\": ").append(room.isCeilingVisible()).append(",\n");
      json.append("      \"level\": \"").append(room.getLevel() != null ? escapeJson(room.getLevel().getName()) : "").append("\",\n");
      
      // Room points
      json.append("      \"points\": [\n");
      float[][] points = room.getPoints();
      if (points != null) {
        for (int i = 0; i < points.length; i++) {
          if (i > 0) json.append(",\n");
          json.append("        {\"x\": ").append(points[i][0]).append(", \"y\": ").append(points[i][1]).append("}");
        }
      }
      json.append("\n      ]\n");
      json.append("    }");
    }
    json.append("\n  ]");
  }

  /**
   * Appends furniture information to the JSON.
   */
  private void appendFurniture(StringBuilder json, List<HomePieceOfFurniture> furniture) {
    json.append("  \"furniture\": [\n");
    boolean first = true;
    for (HomePieceOfFurniture piece : furniture) {
      if (!first) json.append(",\n");
      first = false;

      json.append("    {\n");
      json.append("      \"id\": \"").append(piece.hashCode()).append("\",\n");
      json.append("      \"name\": \"").append(escapeJson(piece.getName())).append("\",\n");
      json.append("      \"catalogId\": \"").append(escapeJson(piece.getCatalogId())).append("\",\n");
      json.append("      \"x\": ").append(piece.getX()).append(",\n");
      json.append("      \"y\": ").append(piece.getY()).append(",\n");
      json.append("      \"elevation\": ").append(piece.getElevation()).append(",\n");
      json.append("      \"angle\": ").append(piece.getAngle()).append(",\n");
      json.append("      \"width\": ").append(piece.getWidth()).append(",\n");
      json.append("      \"depth\": ").append(piece.getDepth()).append(",\n");
      json.append("      \"height\": ").append(piece.getHeight()).append(",\n");
      json.append("      \"color\": ").append(piece.getColor() != null ? piece.getColor() : "null").append(",\n");
      json.append("      \"visible\": ").append(piece.isVisible()).append(",\n");
      json.append("      \"movable\": ").append(piece.isMovable()).append(",\n");
      json.append("      \"doorOrWindow\": ").append(piece.isDoorOrWindow()).append(",\n");
      json.append("      \"level\": \"").append(piece.getLevel() != null ? escapeJson(piece.getLevel().getName()) : "").append("\"\n");
      json.append("    }");
    }
    json.append("\n  ]");
  }

  /**
   * Appends doors and windows information to the JSON.
   */
  private void appendDoorsAndWindows(StringBuilder json, List<HomePieceOfFurniture> furniture) {
    json.append("  \"doorsAndWindows\": [\n");
    boolean first = true;
    for (HomePieceOfFurniture piece : furniture) {
      if (piece.isDoorOrWindow()) {
        if (!first) json.append(",\n");
        first = false;

        json.append("    {\n");
        json.append("      \"id\": \"").append(piece.hashCode()).append("\",\n");
        json.append("      \"name\": \"").append(escapeJson(piece.getName())).append("\",\n");
        json.append("      \"x\": ").append(piece.getX()).append(",\n");
        json.append("      \"y\": ").append(piece.getY()).append(",\n");
        json.append("      \"elevation\": ").append(piece.getElevation()).append(",\n");
        json.append("      \"angle\": ").append(piece.getAngle()).append(",\n");
        json.append("      \"width\": ").append(piece.getWidth()).append(",\n");
        json.append("      \"depth\": ").append(piece.getDepth()).append(",\n");
        json.append("      \"height\": ").append(piece.getHeight()).append(",\n");
        json.append("      \"level\": \"").append(piece.getLevel() != null ? escapeJson(piece.getLevel().getName()) : "").append("\"\n");
        json.append("    }");
      }
    }
    json.append("\n  ]");
  }

  /**
   * Appends level information to the JSON.
   */
  private void appendLevels(StringBuilder json, List<Level> levels) {
    json.append("  \"levels\": [\n");
    boolean first = true;
    for (Level level : levels) {
      if (!first) json.append(",\n");
      first = false;

      json.append("    {\n");
      json.append("      \"name\": \"").append(escapeJson(level.getName())).append("\",\n");
      json.append("      \"elevation\": ").append(level.getElevation()).append(",\n");
      json.append("      \"floorThickness\": ").append(level.getFloorThickness()).append(",\n");
      json.append("      \"height\": ").append(level.getHeight()).append(",\n");
      json.append("      \"elevationIndex\": ").append(level.getElevationIndex()).append(",\n");
      json.append("      \"visible\": ").append(level.isVisible()).append(",\n");
      json.append("      \"viewable\": ").append(level.isViewable()).append("\n");
      json.append("    }");
    }
    json.append("\n  ]");
  }

  /**
   * Appends environment settings to the JSON.
   */
  private void appendEnvironment(StringBuilder json, Home home) {
    json.append("  \"environment\": {\n");

    HomeEnvironment environment = home.getEnvironment();
    if (environment != null) {
      json.append("    \"groundColor\": ").append(environment.getGroundColor()).append(",\n");
      json.append("    \"skyColor\": ").append(environment.getSkyColor()).append(",\n");
      json.append("    \"lightColor\": ").append(environment.getLightColor()).append(",\n");
      json.append("    \"wallsAlpha\": ").append(environment.getWallsAlpha()).append(",\n");
      json.append("    \"allLevelsVisible\": ").append(environment.isAllLevelsVisible()).append(",\n");
      json.append("    \"observerCameraElevationAdjusted\": ").append(environment.isObserverCameraElevationAdjusted()).append("\n");
    } else {
      json.append("    \"present\": false\n");
    }

    json.append("  }");
  }

  /**
   * Escapes special characters in JSON strings.
   */
  private String escapeJson(String str) {
    if (str == null) {
      return "";
    }
    return str.replace("\\", "\\\\")
              .replace("\"", "\\\"")
              .replace("\n", "\\n")
              .replace("\r", "\\r")
              .replace("\t", "\\t");
  }
}
