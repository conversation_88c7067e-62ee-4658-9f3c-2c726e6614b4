/*
 * AIChatDialog.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>na
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.UserPreferences;
import com.eteks.sweethome3d.swing.SwingTools;
import com.eteks.sweethome3d.viewcontroller.HomeController;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.text.BadLocationException;
import javax.swing.text.SimpleAttributeSet;
import javax.swing.text.StyleConstants;
import javax.swing.text.StyledDocument;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.util.concurrent.CompletableFuture;

/**
 * Dialog for AI-powered floor plan analysis chat interface.
 * Provides a conversational interface for users to interact with AI
 * about their floor plan designs.
 * 
 * <AUTHOR> Kpassegna
 */
public class AIChatDialog extends JDialog {
  private final HomeController homeController;
  private final AIIntegration aiIntegration;
  private final UserPreferences preferences;
  
  private JTextPane chatArea;
  private JTextField inputField;
  private JButton sendButton;
  private JButton analyzeButton;
  private JProgressBar progressBar;
  private StyledDocument chatDocument;
  
  private SimpleAttributeSet userStyle;
  private SimpleAttributeSet aiStyle;
  private SimpleAttributeSet systemStyle;
  
  /**
   * Creates a new AI chat dialog.
   */
  public AIChatDialog(HomeController homeController, AIIntegration aiIntegration) {
    super(SwingUtilities.getWindowAncestor((java.awt.Component)homeController.getView()), true);
    this.homeController = homeController;
    this.aiIntegration = aiIntegration;
    this.preferences = homeController.getHome().getUserPreferences();
    
    initializeStyles();
    createComponents();
    layoutComponents();
    setupEventHandlers();
    
    setTitle(getLocalizedString("AIChatDialog.title", "AI Floor Plan Analysis"));
    setSize(800, 600);
    setLocationRelativeTo(getOwner());
    
    // Add welcome message
    addSystemMessage(getLocalizedString("AIChatDialog.welcome", 
        "Welcome to AI Floor Plan Analysis! Click 'Analyze Floor Plan' to get started, or ask me any questions about your design."));
  }
  
  /**
   * Initializes text styles for different message types.
   */
  private void initializeStyles() {
    userStyle = new SimpleAttributeSet();
    StyleConstants.setForeground(userStyle, new Color(0, 100, 200));
    StyleConstants.setBold(userStyle, true);
    
    aiStyle = new SimpleAttributeSet();
    StyleConstants.setForeground(aiStyle, new Color(50, 150, 50));
    
    systemStyle = new SimpleAttributeSet();
    StyleConstants.setForeground(systemStyle, Color.GRAY);
    StyleConstants.setItalic(systemStyle, true);
  }
  
  /**
   * Creates the dialog components.
   */
  private void createComponents() {
    chatArea = new JTextPane();
    chatArea.setEditable(false);
    chatArea.setBackground(Color.WHITE);
    chatArea.setBorder(new EmptyBorder(10, 10, 10, 10));
    chatDocument = chatArea.getStyledDocument();
    
    inputField = new JTextField();
    inputField.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 14));
    
    sendButton = new JButton(getLocalizedString("AIChatDialog.send", "Send"));
    analyzeButton = new JButton(getLocalizedString("AIChatDialog.analyze", "Analyze Floor Plan"));
    
    progressBar = new JProgressBar();
    progressBar.setIndeterminate(true);
    progressBar.setVisible(false);
  }
  
  /**
   * Layouts the dialog components.
   */
  private void layoutComponents() {
    setLayout(new BorderLayout());
    
    // Chat area with scroll pane
    JScrollPane chatScrollPane = new JScrollPane(chatArea);
    chatScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
    chatScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
    add(chatScrollPane, BorderLayout.CENTER);
    
    // Input panel
    JPanel inputPanel = new JPanel(new BorderLayout(5, 5));
    inputPanel.setBorder(new EmptyBorder(10, 10, 10, 10));
    
    JPanel textInputPanel = new JPanel(new BorderLayout(5, 0));
    textInputPanel.add(inputField, BorderLayout.CENTER);
    textInputPanel.add(sendButton, BorderLayout.EAST);
    
    inputPanel.add(textInputPanel, BorderLayout.CENTER);
    inputPanel.add(analyzeButton, BorderLayout.WEST);
    inputPanel.add(progressBar, BorderLayout.SOUTH);
    
    add(inputPanel, BorderLayout.SOUTH);
  }
  
  /**
   * Sets up event handlers for the dialog components.
   */
  private void setupEventHandlers() {
    sendButton.addActionListener(new ActionListener() {
      @Override
      public void actionPerformed(ActionEvent e) {
        sendMessage();
      }
    });
    
    analyzeButton.addActionListener(new ActionListener() {
      @Override
      public void actionPerformed(ActionEvent e) {
        analyzeFloorPlan();
      }
    });
    
    inputField.addKeyListener(new KeyAdapter() {
      @Override
      public void keyPressed(KeyEvent e) {
        if (e.getKeyCode() == KeyEvent.VK_ENTER) {
          sendMessage();
        }
      }
    });
  }
  
  /**
   * Sends a user message to the AI.
   */
  private void sendMessage() {
    String message = inputField.getText().trim();
    if (message.isEmpty()) {
      return;
    }
    
    addUserMessage(message);
    inputField.setText("");
    
    setUIEnabled(false);
    
    Home home = homeController.getHome();
    CompletableFuture<String> future = aiIntegration.askQuestion(home, message);
    
    future.thenAccept(response -> {
      SwingUtilities.invokeLater(() -> {
        addAIMessage(response);
        setUIEnabled(true);
      });
    }).exceptionally(throwable -> {
      SwingUtilities.invokeLater(() -> {
        addSystemMessage(getLocalizedString("AIChatDialog.error", 
            "Error: ") + throwable.getMessage());
        setUIEnabled(true);
      });
      return null;
    });
  }
  
  /**
   * Performs a full floor plan analysis.
   */
  private void analyzeFloorPlan() {
    Home home = homeController.getHome();
    if (home == null) {
      addSystemMessage(getLocalizedString("AIChatDialog.noHome", 
          "No floor plan is currently open."));
      return;
    }
    
    addSystemMessage(getLocalizedString("AIChatDialog.analyzing", 
        "Analyzing your floor plan..."));
    
    setUIEnabled(false);
    
    CompletableFuture<String> future = aiIntegration.analyzeHome(home);
    
    future.thenAccept(response -> {
      SwingUtilities.invokeLater(() -> {
        addAIMessage(response);
        setUIEnabled(true);
      });
    }).exceptionally(throwable -> {
      SwingUtilities.invokeLater(() -> {
        addSystemMessage(getLocalizedString("AIChatDialog.analysisError", 
            "Analysis failed: ") + throwable.getMessage());
        setUIEnabled(true);
      });
      return null;
    });
  }

  /**
   * Adds a user message to the chat area.
   */
  private void addUserMessage(String message) {
    addMessage("You: " + message + "\n\n", userStyle);
  }

  /**
   * Adds an AI message to the chat area.
   */
  private void addAIMessage(String message) {
    addMessage("AI: " + message + "\n\n", aiStyle);
  }

  /**
   * Adds a system message to the chat area.
   */
  private void addSystemMessage(String message) {
    addMessage("System: " + message + "\n\n", systemStyle);
  }

  /**
   * Adds a message to the chat area with the specified style.
   */
  private void addMessage(String message, SimpleAttributeSet style) {
    try {
      chatDocument.insertString(chatDocument.getLength(), message, style);
      chatArea.setCaretPosition(chatDocument.getLength());
    } catch (BadLocationException e) {
      // Should not happen
      e.printStackTrace();
    }
  }

  /**
   * Enables or disables the UI components during AI processing.
   */
  private void setUIEnabled(boolean enabled) {
    sendButton.setEnabled(enabled);
    analyzeButton.setEnabled(enabled);
    inputField.setEnabled(enabled);
    progressBar.setVisible(!enabled);
  }

  /**
   * Gets a localized string from the preferences.
   */
  private String getLocalizedString(String key, String defaultValue) {
    try {
      return preferences.getLocalizedString(AIChatDialog.class, key);
    } catch (IllegalArgumentException ex) {
      return defaultValue;
    }
  }
}
