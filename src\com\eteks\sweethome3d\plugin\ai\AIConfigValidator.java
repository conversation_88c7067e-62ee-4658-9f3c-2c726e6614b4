/*
 * AIConfigValidator.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * Validates AI provider configurations to ensure they are properly formatted
 * and contain all required information.
 * 
 * <AUTHOR>
 */
public class AIConfigValidator {
  
  /**
   * Validates an AI provider configuration.
   * 
   * @param config The configuration to validate
   * @return A ValidationResult indicating success or failure with error messages
   */
  public ValidationResult validate(AIProviderConfig config) {
    if (config == null) {
      return ValidationResult.failure("Configuration cannot be null");
    }
    
    List<String> errors = new ArrayList<>();
    
    // Validate base URL
    validateBaseUrl(config.getBaseUrl(), errors);
    
    // Validate model
    validateModel(config.getModel(), errors);
    
    // Validate API key (conditional)
    validateApiKey(config, errors);
    
    // Validate model parameters
    validateModelParameters(config.getModelParams(), errors);
    
    if (errors.isEmpty()) {
      return ValidationResult.success();
    } else {
      return ValidationResult.failure(errors);
    }
  }
  
  /**
   * Validates the base URL.
   */
  private void validateBaseUrl(String baseUrl, List<String> errors) {
    if (baseUrl == null || baseUrl.trim().isEmpty()) {
      errors.add("Base URL is required");
      return;
    }
    
    try {
      URL url = new URL(baseUrl.trim());
      String protocol = url.getProtocol();
      if (!"http".equals(protocol) && !"https".equals(protocol)) {
        errors.add("Base URL must use HTTP or HTTPS protocol");
      }
    } catch (MalformedURLException e) {
      errors.add("Base URL is not a valid URL: " + e.getMessage());
    }
  }
  
  /**
   * Validates the model name.
   */
  private void validateModel(String model, List<String> errors) {
    if (model == null || model.trim().isEmpty()) {
      errors.add("Model name is required");
      return;
    }
    
    // Basic model name validation
    String trimmedModel = model.trim();
    if (trimmedModel.length() > 200) {
      errors.add("Model name is too long (maximum 200 characters)");
    }
    
    // Check for invalid characters
    if (trimmedModel.contains("\n") || trimmedModel.contains("\r")) {
      errors.add("Model name cannot contain line breaks");
    }
  }
  
  /**
   * Validates the API key based on the provider type.
   */
  private void validateApiKey(AIProviderConfig config, List<String> errors) {
    String baseUrl = config.getBaseUrl();
    String apiKey = config.getApiKey();
    
    if (baseUrl == null) {
      return; // Base URL validation will catch this
    }
    
    // Check if this is a local provider
    String lowerBaseUrl = baseUrl.toLowerCase();
    boolean isLocal = lowerBaseUrl.contains("localhost") || 
                     lowerBaseUrl.contains("127.0.0.1") || 
                     lowerBaseUrl.contains("0.0.0.0");
    
    // API key is required for remote providers
    if (!isLocal && (apiKey == null || apiKey.trim().isEmpty())) {
      errors.add("API Key is required for remote providers");
      return;
    }
    
    // Validate API key format if provided
    if (apiKey != null && !apiKey.trim().isEmpty()) {
      String trimmedKey = apiKey.trim();
      
      if (trimmedKey.length() < 10) {
        errors.add("API Key appears to be too short");
      }
      
      if (trimmedKey.length() > 500) {
        errors.add("API Key is too long (maximum 500 characters)");
      }
      
      // Check for invalid characters
      if (trimmedKey.contains("\n") || trimmedKey.contains("\r")) {
        errors.add("API Key cannot contain line breaks");
      }
    }
  }
  
  /**
   * Validates model parameters.
   */
  private void validateModelParameters(AIModelParameters params, List<String> errors) {
    if (params == null) {
      return; // Parameters are optional, defaults will be used
    }
    
    // Validate temperature
    double temperature = params.getTemperature();
    if (temperature < 0.0 || temperature > 2.0) {
      errors.add("Temperature must be between 0.0 and 2.0");
    }
    
    // Validate max tokens
    int maxTokens = params.getMaxTokens();
    if (maxTokens < 1 || maxTokens > 32768) {
      errors.add("Max tokens must be between 1 and 32768");
    }
    
    // Validate top-p
    double topP = params.getTopP();
    if (topP < 0.0 || topP > 1.0) {
      errors.add("Top-p must be between 0.0 and 1.0");
    }
    
    // Validate frequency penalty
    int frequencyPenalty = params.getFrequencyPenalty();
    if (frequencyPenalty < -2 || frequencyPenalty > 2) {
      errors.add("Frequency penalty must be between -2 and 2");
    }
    
    // Validate presence penalty
    int presencePenalty = params.getPresencePenalty();
    if (presencePenalty < -2 || presencePenalty > 2) {
      errors.add("Presence penalty must be between -2 and 2");
    }
  }
  
  /**
   * Performs a quick validation to check if the configuration has the minimum required fields.
   */
  public boolean hasMinimumRequiredFields(AIProviderConfig config) {
    if (config == null) {
      return false;
    }
    
    return config.getBaseUrl() != null && !config.getBaseUrl().trim().isEmpty() &&
           config.getModel() != null && !config.getModel().trim().isEmpty();
  }
}
