/*
 * AISettingsDialog.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>na
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.UserPreferences;
import com.eteks.sweethome3d.swing.SwingTools;
import com.eteks.sweethome3d.viewcontroller.HomeController;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * Dialog for configuring AI provider settings.
 * Allows users to select and configure AI providers for floor plan analysis.
 * 
 * <AUTHOR>
 */
public class AISettingsDialog extends JDialog {
  private final HomeController homeController;
  private final AIIntegration aiIntegration;
  private final UserPreferences preferences;
  
  private JComboBox<AIProviderPreset> providerComboBox;
  private JTextField baseUrlField;
  private JTextField apiKeyField;
  private JTextField modelField;
  private JSpinner temperatureSpinner;
  private JSpinner maxTokensSpinner;
  private JSpinner topPSpinner;
  
  private JCheckBox excludePersonalInfoCheckBox;
  private JCheckBox excludeLocationDataCheckBox;
  private JCheckBox excludeFurnitureDetailsCheckBox;
  
  private JButton testConnectionButton;
  private JButton saveButton;
  private JButton cancelButton;
  
  private boolean configurationSaved = false;
  
  /**
   * Creates a new AI settings dialog.
   */
  public AISettingsDialog(HomeController homeController, AIIntegration aiIntegration) {
    super(SwingUtilities.getWindowAncestor((java.awt.Component)homeController.getView()), true);
    this.homeController = homeController;
    this.aiIntegration = aiIntegration;
    this.preferences = homeController.getHome().getUserPreferences();
    
    createComponents();
    layoutComponents();
    setupEventHandlers();
    loadCurrentConfiguration();
    
    setTitle(getLocalizedString("AISettingsDialog.title", "AI Settings"));
    setSize(600, 500);
    setLocationRelativeTo(getOwner());
  }
  
  /**
   * Creates the dialog components.
   */
  private void createComponents() {
    // Provider selection
    providerComboBox = new JComboBox<>(AIProviderPreset.values());
    providerComboBox.setRenderer(new DefaultListCellRenderer() {
      @Override
      public Component getListCellRendererComponent(JList<?> list, Object value, 
          int index, boolean isSelected, boolean cellHasFocus) {
        super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
        if (value instanceof AIProviderPreset) {
          setText(((AIProviderPreset) value).getDisplayName());
        }
        return this;
      }
    });
    
    // Configuration fields
    baseUrlField = new JTextField(30);
    apiKeyField = new JPasswordField(30);
    modelField = new JTextField(30);
    
    // Model parameters
    temperatureSpinner = new JSpinner(new SpinnerNumberModel(0.7, 0.0, 2.0, 0.1));
    maxTokensSpinner = new JSpinner(new SpinnerNumberModel(2048, 1, 8192, 256));
    topPSpinner = new JSpinner(new SpinnerNumberModel(1.0, 0.0, 1.0, 0.1));
    
    // Privacy settings
    excludePersonalInfoCheckBox = new JCheckBox(getLocalizedString("AISettingsDialog.excludePersonalInfo", 
        "Exclude personal information"));
    excludeLocationDataCheckBox = new JCheckBox(getLocalizedString("AISettingsDialog.excludeLocationData", 
        "Exclude location data"));
    excludeFurnitureDetailsCheckBox = new JCheckBox(getLocalizedString("AISettingsDialog.excludeFurnitureDetails", 
        "Exclude furniture details"));
    
    // Buttons
    testConnectionButton = new JButton(getLocalizedString("AISettingsDialog.testConnection", "Test Connection"));
    saveButton = new JButton(getLocalizedString("AISettingsDialog.save", "Save"));
    cancelButton = new JButton(getLocalizedString("AISettingsDialog.cancel", "Cancel"));
  }
  
  /**
   * Layouts the dialog components.
   */
  private void layoutComponents() {
    setLayout(new BorderLayout());
    
    JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
    mainPanel.setBorder(new EmptyBorder(15, 15, 15, 15));
    
    // Provider configuration panel
    JPanel configPanel = createConfigurationPanel();
    mainPanel.add(configPanel, BorderLayout.CENTER);
    
    // Privacy settings panel
    JPanel privacyPanel = createPrivacyPanel();
    mainPanel.add(privacyPanel, BorderLayout.SOUTH);
    
    add(mainPanel, BorderLayout.CENTER);
    
    // Button panel
    JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
    buttonPanel.add(testConnectionButton);
    buttonPanel.add(saveButton);
    buttonPanel.add(cancelButton);
    add(buttonPanel, BorderLayout.SOUTH);
  }
  
  /**
   * Creates the provider configuration panel.
   */
  private JPanel createConfigurationPanel() {
    JPanel panel = new JPanel(new GridBagLayout());
    panel.setBorder(new TitledBorder(getLocalizedString("AISettingsDialog.providerConfig", 
        "Provider Configuration")));
    
    GridBagConstraints gbc = new GridBagConstraints();
    gbc.insets = new Insets(5, 5, 5, 5);
    gbc.anchor = GridBagConstraints.WEST;
    
    // Provider selection
    gbc.gridx = 0; gbc.gridy = 0;
    panel.add(new JLabel(getLocalizedString("AISettingsDialog.provider", "Provider:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    panel.add(providerComboBox, gbc);
    
    // Base URL
    gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0.0;
    panel.add(new JLabel(getLocalizedString("AISettingsDialog.baseUrl", "Base URL:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    panel.add(baseUrlField, gbc);
    
    // API Key
    gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0.0;
    panel.add(new JLabel(getLocalizedString("AISettingsDialog.apiKey", "API Key:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    panel.add(apiKeyField, gbc);
    
    // Model
    gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0.0;
    panel.add(new JLabel(getLocalizedString("AISettingsDialog.model", "Model:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    panel.add(modelField, gbc);
    
    // Model parameters
    JPanel paramsPanel = new JPanel(new GridLayout(1, 6, 5, 5));
    paramsPanel.add(new JLabel(getLocalizedString("AISettingsDialog.temperature", "Temperature:")));
    paramsPanel.add(temperatureSpinner);
    paramsPanel.add(new JLabel(getLocalizedString("AISettingsDialog.maxTokens", "Max Tokens:")));
    paramsPanel.add(maxTokensSpinner);
    paramsPanel.add(new JLabel(getLocalizedString("AISettingsDialog.topP", "Top P:")));
    paramsPanel.add(topPSpinner);
    
    gbc.gridx = 0; gbc.gridy = 4; gbc.gridwidth = 2;
    panel.add(paramsPanel, gbc);
    
    return panel;
  }
  
  /**
   * Creates the privacy settings panel.
   */
  private JPanel createPrivacyPanel() {
    JPanel panel = new JPanel(new GridLayout(3, 1, 5, 5));
    panel.setBorder(new TitledBorder(getLocalizedString("AISettingsDialog.privacySettings", 
        "Privacy Settings")));
    
    panel.add(excludePersonalInfoCheckBox);
    panel.add(excludeLocationDataCheckBox);
    panel.add(excludeFurnitureDetailsCheckBox);
    
    return panel;
  }

  /**
   * Sets up event handlers for the dialog components.
   */
  private void setupEventHandlers() {
    providerComboBox.addActionListener(new ActionListener() {
      @Override
      public void actionPerformed(ActionEvent e) {
        updateFieldsFromPreset();
      }
    });

    testConnectionButton.addActionListener(new ActionListener() {
      @Override
      public void actionPerformed(ActionEvent e) {
        testConnection();
      }
    });

    saveButton.addActionListener(new ActionListener() {
      @Override
      public void actionPerformed(ActionEvent e) {
        saveConfiguration();
      }
    });

    cancelButton.addActionListener(new ActionListener() {
      @Override
      public void actionPerformed(ActionEvent e) {
        dispose();
      }
    });
  }

  /**
   * Updates the form fields based on the selected preset.
   */
  private void updateFieldsFromPreset() {
    AIProviderPreset preset = (AIProviderPreset) providerComboBox.getSelectedItem();
    if (preset != null && preset != AIProviderPreset.CUSTOM) {
      AIProviderConfig config = preset.createConfig();
      baseUrlField.setText(config.getBaseUrl());
      modelField.setText(config.getModel());

      // Clear API key when switching presets
      apiKeyField.setText("");
    }
  }

  /**
   * Loads the current configuration into the form.
   */
  private void loadCurrentConfiguration() {
    AIProviderConfig config = aiIntegration.getCurrentConfiguration();
    if (config != null) {
      // Try to match with a preset
      AIProviderPreset matchingPreset = findMatchingPreset(config);
      if (matchingPreset != null) {
        providerComboBox.setSelectedItem(matchingPreset);
      } else {
        providerComboBox.setSelectedItem(AIProviderPreset.CUSTOM);
      }

      baseUrlField.setText(config.getBaseUrl());
      apiKeyField.setText(config.getApiKey() != null ? config.getApiKey() : "");
      modelField.setText(config.getModel());

      AIModelParameters params = config.getModelParams();
      if (params != null) {
        temperatureSpinner.setValue(params.getTemperature());
        maxTokensSpinner.setValue(params.getMaxTokens());
        topPSpinner.setValue(params.getTopP());
      }
    }

    // Load privacy settings
    PrivacyManager privacyManager = aiIntegration.getPrivacyManager();
    excludePersonalInfoCheckBox.setSelected(privacyManager.shouldExcludePersonalInfo());
    excludeLocationDataCheckBox.setSelected(privacyManager.shouldExcludeLocationData());
    excludeFurnitureDetailsCheckBox.setSelected(privacyManager.shouldExcludeFurnitureDetails());
  }

  /**
   * Finds a matching preset for the given configuration.
   */
  private AIProviderPreset findMatchingPreset(AIProviderConfig config) {
    for (AIProviderPreset preset : AIProviderPreset.values()) {
      if (preset != AIProviderPreset.CUSTOM &&
          preset.getBaseUrl().equals(config.getBaseUrl())) {
        return preset;
      }
    }
    return null;
  }

  /**
   * Tests the connection to the AI service.
   */
  private void testConnection() {
    AIProviderConfig config = createConfigFromForm();
    ValidationResult validation = validateConfiguration(config);

    if (!validation.isValid()) {
      JOptionPane.showMessageDialog(this,
          getLocalizedString("AISettingsDialog.validationError", "Configuration Error") +
          ":\n" + validation.getErrorMessage(),
          getLocalizedString("AISettingsDialog.error", "Error"),
          JOptionPane.ERROR_MESSAGE);
      return;
    }

    testConnectionButton.setEnabled(false);
    testConnectionButton.setText(getLocalizedString("AISettingsDialog.testing", "Testing..."));

    SwingUtilities.invokeLater(() -> {
      try {
        AIClient testClient = AIClientFactory.createClient(config);
        boolean success = testClient.testConnection();
        testClient.close();

        SwingUtilities.invokeLater(() -> {
          if (success) {
            JOptionPane.showMessageDialog(this,
                getLocalizedString("AISettingsDialog.connectionSuccess", "Connection successful!"),
                getLocalizedString("AISettingsDialog.success", "Success"),
                JOptionPane.INFORMATION_MESSAGE);
          } else {
            JOptionPane.showMessageDialog(this,
                getLocalizedString("AISettingsDialog.connectionFailed", "Connection failed."),
                getLocalizedString("AISettingsDialog.error", "Error"),
                JOptionPane.ERROR_MESSAGE);
          }

          testConnectionButton.setEnabled(true);
          testConnectionButton.setText(getLocalizedString("AISettingsDialog.testConnection", "Test Connection"));
        });
      } catch (Exception e) {
        SwingUtilities.invokeLater(() -> {
          JOptionPane.showMessageDialog(this,
              getLocalizedString("AISettingsDialog.connectionError", "Connection error") +
              ":\n" + e.getMessage(),
              getLocalizedString("AISettingsDialog.error", "Error"),
              JOptionPane.ERROR_MESSAGE);

          testConnectionButton.setEnabled(true);
          testConnectionButton.setText(getLocalizedString("AISettingsDialog.testConnection", "Test Connection"));
        });
      }
    });
  }

  /**
   * Saves the configuration.
   */
  private void saveConfiguration() {
    AIProviderConfig config = createConfigFromForm();
    ValidationResult validation = validateConfiguration(config);

    if (!validation.isValid()) {
      JOptionPane.showMessageDialog(this,
          getLocalizedString("AISettingsDialog.validationError", "Configuration Error") +
          ":\n" + validation.getErrorMessage(),
          getLocalizedString("AISettingsDialog.error", "Error"),
          JOptionPane.ERROR_MESSAGE);
      return;
    }

    // Save AI configuration
    aiIntegration.saveConfiguration(config);

    // Save privacy settings
    PrivacyManager privacyManager = aiIntegration.getPrivacyManager();
    privacyManager.setExcludePersonalInfo(excludePersonalInfoCheckBox.isSelected());
    privacyManager.setExcludeLocationData(excludeLocationDataCheckBox.isSelected());
    privacyManager.setExcludeFurnitureDetails(excludeFurnitureDetailsCheckBox.isSelected());

    configurationSaved = true;
    dispose();
  }

  /**
   * Creates a configuration object from the form fields.
   */
  private AIProviderConfig createConfigFromForm() {
    AIProviderConfig config = new AIProviderConfig();

    AIProviderPreset preset = (AIProviderPreset) providerComboBox.getSelectedItem();
    config.setProviderName(preset != null ? preset.getDisplayName() : "Custom");
    config.setBaseUrl(baseUrlField.getText().trim());
    config.setApiKey(apiKeyField.getText().trim());
    config.setModel(modelField.getText().trim());

    AIModelParameters params = new AIModelParameters();
    params.setTemperature((Double) temperatureSpinner.getValue());
    params.setMaxTokens((Integer) maxTokensSpinner.getValue());
    params.setTopP((Double) topPSpinner.getValue());
    config.setModelParams(params);

    return config;
  }

  /**
   * Validates the configuration.
   */
  private ValidationResult validateConfiguration(AIProviderConfig config) {
    AIConfigValidator validator = new AIConfigValidator();
    return validator.validate(config);
  }

  /**
   * Returns whether the configuration was saved.
   */
  public boolean isConfigurationSaved() {
    return configurationSaved;
  }

  /**
   * Gets a localized string from the preferences.
   */
  private String getLocalizedString(String key, String defaultValue) {
    try {
      return preferences.getLocalizedString(AISettingsDialog.class, key);
    } catch (IllegalArgumentException ex) {
      return defaultValue;
    }
  }
}
